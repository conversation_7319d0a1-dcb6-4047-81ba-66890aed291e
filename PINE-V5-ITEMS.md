# PINE ERRORS YOU CONTINUE TO MAKE
## Cannot use 'plot' in local scope
### INCORRECT:
```
if isTfEnabled(3)
    state = array.get(states, 3)
    rfValid = array.get(validFlags, 3)
    zscore = array.get(adxZscores, 3)
    slope = array.get(adxSlopes, 3)
    minAc = getConfigValue("minAc", 3)
    minSl = getConfigValue("minSl", 3)
    plot(state == StrongTrend ? -65 : na, title="TF4 Trend Strong", style=plot.style_circles, color=color.green)
```

## 'SOME-VARIABLE' is already defined
### You arent considering the context of my variables & usage
```
entrySignal' is already defined
```

```
[entrySignal, signalDir] = generateSignal(validCount, directionSum, compositeScore, cfgScreener)
```
#### but above in the FULL-CODE.pinescript, you have:
```
var bool  entrySignal     = false
```

## 'CHECKING FOR ERRORS' in the IDE is not how to debug, VSC doesnt support pinescript natively
### you must adhere to https://www.tradingview.com/pine-script-reference/v5/
### I have a VSC extension which may help you if you can use it, its called 'Tag Pinescript Functions' & a second is called 'PineScript Helper'
### Use Tag Pinescript Functions to tag & outline all usage in my files for your ease of modifications, understanding etc.

# OPTIMIZATION GOALS FOR FULL-CODE.PINESCRIPT
1. Reduce code length while maintaining all functionality
2. Focus on consolidating redundant patterns, especially in timeframe handling
3. Avoid using functions for plotting (plot statements must be at global scope)
4. Be careful with variable redefinition
5. Maintain readability and structure
6. Ensure the code stays within token limits for complete review

## Implemented Optimizations
- Fixed critical error: Moved plot functions to global scope by calculating conditional values first
- Properly defined type definitions with each property on its own line
- Consolidated timeframe handling by calculating all values before plotting
- Maintained proper syntax for PineScript v5
- Reduced redundancy in the code
- Improved readability with better organization
- Implemented the "Features" pattern from EXAMPLE-PINE.pinescript for timeframe settings
- Used arrays to store timeframe settings and access them efficiently
- Simplified helper functions to use the arrays directly

## Key PineScript v5 Rules
1. Plot functions must be at global scope (not inside if statements, loops, or functions)
2. Type definitions must have each property on its own line
3. Variable redefinition must be handled carefully (use := for reassignment)
4. Conditional logic should be moved inside plot arguments rather than wrapping plot calls
5. Each variable declaration should be on its own line
6. Use arrays to store related settings and access them efficiently

## The "Features" Pattern
The "Features" pattern from EXAMPLE-PINE.pinescript is a way to handle multiple similar settings efficiently:

1. Define a set of inputs for each feature/timeframe
2. Store these settings in arrays for easy access
3. Use helper functions to access the settings by index
4. Create objects that use these settings

This pattern reduces code duplication and improves maintainability by:
- Centralizing related settings
- Making it easy to add or remove features/timeframes
- Providing a consistent way to access settings
- Reducing the number of variables needed


## UNDEFINED / SYNTAX ERRORS
### Incorrect:
```
type cfgScreener
    bool useTF1, useTF2, useTF3, useTF4, minDir_tf, minAcc_tf, minSlp_tf
```
### Correct:
```
type cfgScreener
    bool useTF1
    bool useTF2
    bool useTF3
    bool useTF4
    bool minDir_tf
    bool minAcc_tf
    bool minSlp_tf
```
### INCORRECT:
```
validCount = 0, directionSum = 0, compositeScore = 0.0
```
### CORRECT:
```
validCount := 0
directionSum := 0
compositeScore := 0.0
```
