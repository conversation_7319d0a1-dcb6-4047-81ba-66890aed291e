// This Pine Script® code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © cryptokairo
//@version=5
indicator("MTF Generic Analyzer", overlay=false)

// ===========================
// ==== Constants & Colors ===
// ===========================

COLOR_GRAY           = color.rgb(87, 89, 94)
COLOR_BLUE           = color.rgb(64, 118, 179)
COLOR_TREND_BULL     = color.rgb(16 , 228, 217)
COLOR_TREND_BEAR     = color.rgb(201, 206, 63)
COLOR_SUPPLY         = color.rgb(170, 0  , 10)
COLOR_DEMAND         = color.rgb(0  , 190, 120)
COLOR_SLOPE_UP       = color.rgb(147, 212, 68)
COLOR_SLOPE_DOWN     = color.rgb(206, 60 , 140)
COLOR_SLOPE_NEUTRAL  = color.rgb(165, 165, 165)
COLOR_CMO_M1         = color.rgb(76, 175, 79, 50)
COLOR_CMO_M2         = color.rgb(255, 235, 59, 50)
COLOR_CMO_M3         = color.rgb(255, 152, 0, 50)
COLOR_CM0_M0         = color.rgb(255, 82, 82, 50)

// ===========================
// ==== Type Definitions =====
// ===========================

// Type for indicator settings
type IndicatorSettings
    string name
    string type
    float source
    int period
    int smoothing
    float threshold

// Type for analysis component settings
type AnalysisSettings
    bool useDirection
    bool useSlope
    bool useAcceleration
    float directionThreshold
    float slopeThreshold
    float accelerationThreshold

// Type for timeframe settings
type TimeframeSettings
    string timeframe
    float weight
    bool enabled

// Type for indicator results
type IndicatorResult
    float value
    float normalized
    float direction
    float slope
    float acceleration
    bool directionValid
    bool slopeValid
    bool accelerationValid

// Type for timeframe results
type TimeframeResult
    IndicatorResult[] indicatorResults
    float compositeScore
    float directionSum
    bool isValid

// ===========================
// ==== Helper Functions =====
// ===========================

// Normalize an indicator value to a standard range (-1 to 1)
normalizeIndicator(float value, string type, float min, float max) =>
    result = 0.0
    if type == "ADX"
        // ADX is 0 to 100, normalize to 0 to 1
        result := value / 100
    else if type == "RF"
        // Range Filter is relative to price, normalize based on ATR
        atr = ta.atr(14)
        result := value / (atr * 10)
    else
        // Default normalization using min-max scaling
        rng = max - min
        result := rng != 0 ? (value - min) / rng * 2 - 1 : 0

    // Ensure result is between -1 and 1
    math.max(-1, math.min(1, result))

// Calculate direction of an indicator
analyzeDirection(float current, float previous, float threshold) =>
    direction = current > previous ? 1 : current < previous ? -1 : 0
    isValid = math.abs(current - previous) >= threshold
    [direction, isValid]

// Calculate slope of an indicator
analyzeSlope(float[] values, int length, float threshold) =>
    if array.size(values) < length
        [0.0, false]
    else
        // Calculate linear regression slope
        sumX = 0.0
        sumY = 0.0
        sumXY = 0.0
        sumX2 = 0.0

        for i = 0 to length - 1
            x = i
            y = array.get(values, array.size(values) - 1 - i)
            sumX := sumX + x
            sumY := sumY + y
            sumXY := sumXY + x * y
            sumX2 := sumX2 + x * x

        n = length
        slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX)
        isValid = math.abs(slope) >= threshold

        [slope, isValid]

// Calculate acceleration of an indicator
analyzeAcceleration(float[] slopes, int length, float threshold) =>
    if array.size(slopes) < length
        [0.0, false]
    else
        // Calculate change in slope (acceleration)
        current = array.get(slopes, array.size(slopes) - 1)
        previous = array.get(slopes, array.size(slopes) - 2)
        acceleration = current - previous
        isValid = math.abs(acceleration) >= threshold

        [acceleration, isValid]

// Calculate Z-score
f_zscore(float _src, int _length) =>
    _mean = ta.sma(_src, _length)
    _std = ta.stdev(_src - _mean, _length)
    _value = _std != 0 ? (_src - _mean) / _std : 0
    _value

// Calculate ADX
calcADX(int len) =>
    tr = math.max(math.max(high - low, math.abs(high - nz(close[1]))), math.abs(low - nz(close[1])))
    upMove = high - nz(high[1])
    downMove = nz(low[1]) - low
    plusDM = upMove > downMove and upMove > 0 ? upMove : 0
    minusDM = downMove > upMove and downMove > 0 ? downMove : 0
    trSum = ta.rma(tr, len)
    plusDISum = ta.rma(plusDM, len)
    minusDISum = ta.rma(minusDM, len)
    plusDI = trSum != 0 ? 100 * plusDISum / trSum : 0
    minusDI = trSum != 0 ? 100 * minusDISum / trSum : 0
    dx = (plusDI + minusDI) != 0 ? 100 * math.abs(plusDI - minusDI) / (plusDI + minusDI) : 0
    adx = ta.rma(dx, len)
    [adx, plusDI, minusDI]

// Calculate Range Filter
calcRangeFilter(float src, float smrng, float filtPrev) =>
    filtNew = src > nz(filtPrev) ? (src - smrng < nz(filtPrev) ? nz(filtPrev) : src - smrng) : src + smrng > nz(filtPrev) ? nz(filtPrev) : src + smrng
    filtNew

// ===========================
// ==== Input Definitions ====
// ===========================

// General settings
general_group = "General Settings"
src = input(close, "Source", group=general_group)

// Timeframe settings using the "Features" pattern
timeframe_group = "Timeframe Settings"

// Define default values for timeframes
var string[] tf_names = array.new_string(4)
array.set(tf_names, 0, "TF1")
array.set(tf_names, 1, "TF2")
array.set(tf_names, 2, "TF3")
array.set(tf_names, 3, "TF4")

var string[] tf_periods = array.new_string(4)
array.set(tf_periods, 0, "15")
array.set(tf_periods, 1, "30")
array.set(tf_periods, 2, "45")
array.set(tf_periods, 3, "60")

var float[] tf_default_weights = array.new_float(4)
array.set(tf_default_weights, 0, 1.0)
array.set(tf_default_weights, 1, 0.75)
array.set(tf_default_weights, 2, 0.5)
array.set(tf_default_weights, 3, 0.25)

// Timeframe enable inputs
tf_enabled_1 = input.bool(true, "Enable TF1", group=timeframe_group, inline="enable")
tf_enabled_2 = input.bool(true, "Enable TF2", group=timeframe_group, inline="enable")
tf_enabled_3 = input.bool(true, "Enable TF3", group=timeframe_group, inline="enable")
tf_enabled_4 = input.bool(true, "Enable TF4", group=timeframe_group, inline="enable")

// Timeframe settings - using const values
tf1 = input.timeframe("15", "TF1 Timeframe", group=timeframe_group)
tf2 = input.timeframe("30", "TF2 Timeframe", group=timeframe_group)
tf3 = input.timeframe("45", "TF3 Timeframe", group=timeframe_group)
tf4 = input.timeframe("60", "TF4 Timeframe", group=timeframe_group)

// Feature inputs - these are the generic inputs that will be applied to all timeframes
// This is the key part of the "Features" pattern
feature_group = "Feature Settings"
feature_min_up = input.int(2, "Min Up Bars", minval=1, group=feature_group)
feature_min_dn = input.int(2, "Min Down Bars", minval=1, group=feature_group)
feature_min_ac = input.float(1.0, "Min Acceleration", minval=0.1, group=feature_group)
feature_min_sl = input.float(10.0, "Min Slope", minval=0.1, group=feature_group)
feature_rf_mode = input.int(1, "RF Mode", options=[-1, 0, 1], group=feature_group)
feature_rf_inv = input.bool(false, "Invert RF", group=feature_group)

// Analysis components settings
analysis_group = "Analysis Components"
use_direction = input.bool(true, "Use Direction Analysis", group=analysis_group)
use_slope = input.bool(true, "Use Slope Analysis", group=analysis_group)
use_acceleration = input.bool(true, "Use Acceleration Analysis", group=analysis_group)
direction_threshold = input.float(0.01, "Direction Threshold", minval=0.001, group=analysis_group)
slope_threshold = input.float(0.05, "Slope Threshold", minval=0.001, group=analysis_group)
acceleration_threshold = input.float(0.01, "Acceleration Threshold", minval=0.001, group=analysis_group)

// Indicator settings
indicator_group = "Indicator Settings"
indicator_selector = input.string("ADX", "Indicator Type", options=["ADX", "RF"], group=indicator_group)
indicator_period = input.int(14, "Period", minval=1, group=indicator_group)
indicator_smoothing = input.int(14, "Smoothing", minval=1, group=indicator_group)
indicator_threshold = input.float(20, "Threshold", minval=0, group=indicator_group)

// Composite settings
composite_group = "Composite Settings"
min_valid_timeframes = input.int(2, "Min Valid Timeframes", minval=1, maxval=4, group=composite_group)
min_direction_agreement = input.int(3, "Min Direction Agreement", minval=1, maxval=4, group=composite_group)
min_composite_score = input.float(4.0, "Min Composite Score", step=0.1, group=composite_group)

// ===========================
// ==== Arrays & Storage =====
// ===========================

// Store timeframe settings as constants to avoid series values
var string tf1_const = tf1
var string tf2_const = tf2
var string tf3_const = tf3
var string tf4_const = tf4

// Store timeframe settings in arrays
var float[] tf_weights = array.new_float(4)
var bool[] tf_enabled = array.new_bool(4)
var int[] tf_min_ups = array.new_int(4)
var int[] tf_min_dns = array.new_int(4)
var float[] tf_min_acs = array.new_float(4)
var float[] tf_min_sls = array.new_float(4)
var int[] tf_rf_modes = array.new_int(4)
var bool[] tf_rf_invs = array.new_bool(4)

    // Set weights (could be customized per timeframe if needed)
array.set(tf_weights, 0, array.get(tf_default_weights, 0))
array.set(tf_weights, 1, array.get(tf_default_weights, 1))
array.set(tf_weights, 2, array.get(tf_default_weights, 2))
array.set(tf_weights, 3, array.get(tf_default_weights, 3))

    // Set feature values - using the same values for all timeframes
    // This is the key part of the "Features" pattern
 for i = 0 to 3
    array.set(tf_min_ups, i, feature_min_up)
     array.set(tf_min_dns, i, feature_min_dn)
    array.set(tf_min_acs, i, feature_min_ac)
    array.set(tf_min_sls, i, feature_min_sl)
    array.set(tf_rf_modes, i, feature_rf_mode)
    array.set(tf_rf_invs, i, feature_rf_inv)

// Update enabled flags on each bar
array.set(tf_enabled, 0, tf_enabled_1)
array.set(tf_enabled, 1, tf_enabled_2)
array.set(tf_enabled, 2, tf_enabled_3)
array.set(tf_enabled, 3, tf_enabled_4)

// Create analysis settings object
AnalysisSettings analysisSettings = AnalysisSettings.new(
   use_direction,
   use_slope,
   use_acceleration,
   direction_threshold,
   slope_threshold,
   acceleration_threshold
 )

// Create indicator settings object
IndicatorSettings indicatorSettings = IndicatorSettings.new(
   "Generic Indicator",
   indicator_selector,
   src,
   indicator_period,
   indicator_smoothing,
   indicator_threshold
 )

// ===========================
// ==== Processing Logic =====
// ===========================

// Arrays to store historical values for slope and acceleration calculation
var float[] tf1_values = array.new_float(0)
var float[] tf2_values = array.new_float(0)
var float[] tf3_values = array.new_float(0)
var float[] tf4_values = array.new_float(0)

var float[] tf1_slopes = array.new_float(0)
var float[] tf2_slopes = array.new_float(0)
var float[] tf3_slopes = array.new_float(0)
var float[] tf4_slopes = array.new_float(0)

// Arrays to store RF state
var float[] rfPrevFilters = array.new_float(4, na)
var int[] rfUpCounts = array.new_int(4, 0)
var int[] rfDownCounts = array.new_int(4, 0)

// Create arrays for storing historical values
var float[] tf1_values = array.new_float(0)
var float[] tf2_values = array.new_float(0)
var float[] tf3_values = array.new_float(0)
var float[] tf4_values = array.new_float(0)

// Function to get the appropriate historical values array for a timeframe
getHistoricalValues(int index) =>
    index == 0 ? tf1_values :
     index == 1 ? tf2_values :
     index == 2 ? tf3_values :
     tf4_values

// Function to get the appropriate historical slopes array for a timeframe
getHistoricalSlopes(int index) =>
    index == 0 ? tf1_slopes :
     index == 1 ? tf2_slopes :
     index == 2 ? tf3_slopes :
     tf4_slopes

var int const_period = indicator_period
var int const_smoothing = indicator_smoothing
var string const_type = indicator_selector
var float const_src = src

processIndicator(string timeframe, int index) =>
    result = 0.0
    upCount = 0
    downCount = 0

    if const_type == "ADX"
        [adx, plusDI, minusDI] = request.security(syminfo.tickerid, timeframe, calcADX(const_period))
        result := adx
    else if const_type == "RF"
        avrng = request.security(syminfo.tickerid, timeframe, ta.ema(math.abs(const_src - const_src[1]), const_smoothing))
        smrng = request.security(syminfo.tickerid, timeframe, ta.ema(avrng, const_smoothing * 2 - 1))

        prevFilter = array.get(rfPrevFilters, index)

        newFilter = request.security(syminfo.tickerid, timeframe, calcRangeFilter(const_src, smrng, prevFilter))

        if not na(prevFilter) and not na(newFilter)
            if newFilter > prevFilter
                upCount := array.get(rfUpCounts, index) + 1
                downCount := 0
            else if newFilter < prevFilter
                upCount := 0
                downCount := array.get(rfDownCounts, index) + 1
            else
                upCount := array.get(rfUpCounts, index)
                downCount := array.get(rfDownCounts, index)

        array.set(rfPrevFilters, index, newFilter)
        array.set(rfUpCounts, index, upCount)
        array.set(rfDownCounts, index, downCount)

        result := newFilter

    [result, upCount, downCount]

analyzeIndicatorValue(float value, int upCount, int downCount, int index, AnalysisSettings settings) =>
    historicalValues = getHistoricalValues(index)
    historicalSlopes = getHistoricalSlopes(index)

    if array.size(historicalValues) > 50
        array.shift(historicalValues)

    normalized = normalizeIndicator(value, indicatorSettings.type, 0, 100)

    prevValue = array.size(historicalValues) > 1 ? array.get(historicalValues, array.size(historicalValues) - 2) : value
    [direction, directionValid] = analyzeDirection(value, prevValue, settings.directionThreshold)

    if direction > 0 and upCount < array.get(tf_min_ups, index)
        directionValid := false
    else if direction < 0 and downCount < array.get(tf_min_dns, index)
        directionValid := false

    [slope, slopeValid] = analyzeSlope(historicalValues, math.min(10, array.size(historicalValues)), settings.slopeThreshold)

    if math.abs(slope) < array.get(tf_min_sls, index)
        slopeValid := false

    array.push(historicalSlopes, slope)

    if array.size(historicalSlopes) > 20
        array.shift(historicalSlopes)

    [acceleration, accelerationValid] = analyzeAcceleration(historicalSlopes, math.min(5, array.size(historicalSlopes)), settings.accelerationThreshold)

    if math.abs(acceleration) < array.get(tf_min_acs, index)
        accelerationValid := false

    IndicatorResult.new(
       value,
       normalized,
       direction,
       slope,
       acceleration,
       directionValid,
       slopeValid,
       accelerationValid
     )

processTimeframes() =>
    var TimeframeResult result1 = na
    var TimeframeResult result2 = na
    var TimeframeResult result3 = na
    var TimeframeResult result4 = na

    if barstate.isfirst
        emptyIndicator = IndicatorResult.new(0.0, 0.0, 0, 0.0, 0.0, false, false, false)

        emptyArray1 = array.new<IndicatorResult>(1, emptyIndicator)
        emptyArray2 = array.new<IndicatorResult>(1, emptyIndicator)
        emptyArray3 = array.new<IndicatorResult>(1, emptyIndicator)
        emptyArray4 = array.new<IndicatorResult>(1, emptyIndicator)

        result1 := TimeframeResult.new(emptyArray1, 0.0, 0.0, false)
        result2 := TimeframeResult.new(emptyArray2, 0.0, 0.0, false)
        result3 := TimeframeResult.new(emptyArray3, 0.0, 0.0, false)
        result4 := TimeframeResult.new(emptyArray4, 0.0, 0.0, false)

    for i = 0 to 3
        if array.get(tf_enabled, i)
            // Process indicator for each timeframe using constant values
            float indicatorValue = 0.0
            int upCount = 0
            int downCount = 0

            // Process each timeframe with the appropriate constant
            if i == 0
                resultTuple = processIndicator(tf1_const, i)
                indicatorValue := resultTuple[0]
                upCount := resultTuple[1]
                downCount := resultTuple[2]
            else if i == 1
                resultTuple = processIndicator(tf2_const, i)
                indicatorValue := resultTuple[0]
                upCount := resultTuple[1]
                downCount := resultTuple[2]
            else if i == 2
                resultTuple = processIndicator(tf3_const, i)
                indicatorValue := resultTuple[0]
                upCount := resultTuple[1]
                downCount := resultTuple[2]
            else if i == 3
                resultTuple = processIndicator(tf4_const, i)
                indicatorValue := resultTuple[0]
                upCount := resultTuple[1]
                downCount := resultTuple[2]

            indicatorResult = analyzeIndicatorValue(
               indicatorValue,
               upCount,
               downCount,
               i,
               analysisSettings
             )

            compositeScore = 0.0
            directionSum = 0.0

            if analysisSettings.useDirection and indicatorResult.directionValid
                compositeScore += math.abs(indicatorResult.direction) * array.get(tf_weights, i)
                directionSum += indicatorResult.direction

            if analysisSettings.useSlope and indicatorResult.slopeValid
                compositeScore += math.abs(indicatorResult.slope) * array.get(tf_weights, i)

            if analysisSettings.useAcceleration and indicatorResult.accelerationValid
                compositeScore += math.abs(indicatorResult.acceleration) * array.get(tf_weights, i)

            rfMode = array.get(tf_rf_modes, i)
            rfInvert = array.get(tf_rf_invs, i)

            if rfMode != 0
                if rfMode == 1 and directionSum < 0
                    directionSum := 0.0
                    compositeScore := 0.0
                else if rfMode == -1 and directionSum > 0
                    directionSum := 0.0
                    compositeScore := 0.0

            if rfInvert
                directionSum := -directionSum

            isValid = (not analysisSettings.useDirection or indicatorResult.directionValid) and
                     (not analysisSettings.useSlope or indicatorResult.slopeValid) and
                     (not analysisSettings.useAcceleration or indicatorResult.accelerationValid)

            indicatorArray = array.new<IndicatorResult>(1, indicatorResult)

            timeframeResult = TimeframeResult.new(
               indicatorArray,  // Use array literal instead of array.new_array
               compositeScore,
               directionSum,
               isValid
             )

            if i == 0
                result1 := timeframeResult
            else if i == 1
                result2 := timeframeResult
            else if i == 2
                result3 := timeframeResult
            else if i == 3
                result4 := timeframeResult

    [result1, result2, result3, result4]

generateSignal(TimeframeResult result1, TimeframeResult result2, TimeframeResult result3, TimeframeResult result4) =>
    validCount = 0
    totalDirectionSum = 0.0
    totalCompositeScore = 0.0

    if array.get(tf_enabled, 0) and result1.isValid
        validCount += 1
        totalDirectionSum += result1.directionSum
        totalCompositeScore += result1.compositeScore

    if array.get(tf_enabled, 1) and result2.isValid
        validCount += 1
        totalDirectionSum += result2.directionSum
        totalCompositeScore += result2.compositeScore

    if array.get(tf_enabled, 2) and result3.isValid
        validCount += 1
        totalDirectionSum += result3.directionSum
        totalCompositeScore += result3.compositeScore

    if array.get(tf_enabled, 3) and result4.isValid
        validCount += 1
        totalDirectionSum += result4.directionSum
        totalCompositeScore += result4.compositeScore

    isValid = validCount >= min_valid_timeframes
    hasDirection = math.abs(totalDirectionSum) >= min_direction_agreement
    hasStrength = totalCompositeScore >= min_composite_score
    signal = isValid and hasDirection and hasStrength
    direction = totalDirectionSum > 0 ? 1 : totalDirectionSum < 0 ? -1 : 0

    [signal, direction, validCount, totalDirectionSum, totalCompositeScore]

// ===========================
// ==== Main Calculation =====
// ===========================
[result1, result2, result3, result4] = processTimeframes()

[entrySignal, signalDirection, validCount, directionSum, compositeScore] = generateSignal(result1, result2, result3, result4)

// ===========================
// ======= Plotting ==========
// ===========================
var float[] indicator_values = array.new_float(4, na)
var bool[] direction_valid = array.new_bool(4, false)
var bool[] slope_valid = array.new_bool(4, false)
var bool[] acceleration_valid = array.new_bool(4, false)

if array.get(tf_enabled, 0)
    indicatorResult = array.get(result1.indicatorResults, 0)
    array.set(indicator_values, 0, indicatorResult.value)
    array.set(direction_valid, 0, indicatorResult.directionValid)
    array.set(slope_valid, 0, indicatorResult.slopeValid)
    array.set(acceleration_valid, 0, indicatorResult.accelerationValid)

if array.get(tf_enabled, 1)
    indicatorResult = array.get(result2.indicatorResults, 0)
    array.set(indicator_values, 1, indicatorResult.value)
    array.set(direction_valid, 1, indicatorResult.directionValid)
    array.set(slope_valid, 1, indicatorResult.slopeValid)
    array.set(acceleration_valid, 1, indicatorResult.accelerationValid)

if array.get(tf_enabled, 2)
    indicatorResult = array.get(result3.indicatorResults, 0)
    array.set(indicator_values, 2, indicatorResult.value)
    array.set(direction_valid, 2, indicatorResult.directionValid)
    array.set(slope_valid, 2, indicatorResult.slopeValid)
    array.set(acceleration_valid, 2, indicatorResult.accelerationValid)

if array.get(tf_enabled, 3)
    indicatorResult = array.get(result4.indicatorResults, 0)
    array.set(indicator_values, 3, indicatorResult.value)
    array.set(direction_valid, 3, indicatorResult.directionValid)
    array.set(slope_valid, 3, indicatorResult.slopeValid)
    array.set(acceleration_valid, 3, indicatorResult.accelerationValid)

var float tf1_base = -20
var float tf2_base = -35
var float tf3_base = -50
var float tf4_base = -65

indicator_tf1 = array.get(tf_enabled, 0) ? array.get(indicator_values, 0) : na
indicator_tf2 = array.get(tf_enabled, 1) ? array.get(indicator_values, 1) : na
indicator_tf3 = array.get(tf_enabled, 2) ? array.get(indicator_values, 2) : na
indicator_tf4 = array.get(tf_enabled, 3) ? array.get(indicator_values, 3) : na

dir_valid_tf1 = array.get(tf_enabled, 0) and analysisSettings.useDirection and array.get(direction_valid, 0)
dir_invalid_tf1 = array.get(tf_enabled, 0) and analysisSettings.useDirection and not array.get(direction_valid, 0)
dir_valid_tf2 = array.get(tf_enabled, 1) and analysisSettings.useDirection and array.get(direction_valid, 1)
dir_invalid_tf2 = array.get(tf_enabled, 1) and analysisSettings.useDirection and not array.get(direction_valid, 1)
dir_valid_tf3 = array.get(tf_enabled, 2) and analysisSettings.useDirection and array.get(direction_valid, 2)
dir_invalid_tf3 = array.get(tf_enabled, 2) and analysisSettings.useDirection and not array.get(direction_valid, 2)
dir_valid_tf4 = array.get(tf_enabled, 3) and analysisSettings.useDirection and array.get(direction_valid, 3)
dir_invalid_tf4 = array.get(tf_enabled, 3) and analysisSettings.useDirection and not array.get(direction_valid, 3)

slope_valid_tf1 = array.get(tf_enabled, 0) and analysisSettings.useSlope and array.get(slope_valid, 0)
slope_invalid_tf1 = array.get(tf_enabled, 0) and analysisSettings.useSlope and not array.get(slope_valid, 0)
slope_valid_tf2 = array.get(tf_enabled, 1) and analysisSettings.useSlope and array.get(slope_valid, 1)
slope_invalid_tf2 = array.get(tf_enabled, 1) and analysisSettings.useSlope and not array.get(slope_valid, 1)
slope_valid_tf3 = array.get(tf_enabled, 2) and analysisSettings.useSlope and array.get(slope_valid, 2)
slope_invalid_tf3 = array.get(tf_enabled, 2) and analysisSettings.useSlope and not array.get(slope_valid, 2)
slope_valid_tf4 = array.get(tf_enabled, 3) and analysisSettings.useSlope and array.get(slope_valid, 3)
slope_invalid_tf4 = array.get(tf_enabled, 3) and analysisSettings.useSlope and not array.get(slope_valid, 3)

acc_valid_tf1 = array.get(tf_enabled, 0) and analysisSettings.useAcceleration and array.get(acceleration_valid, 0)
acc_invalid_tf1 = array.get(tf_enabled, 0) and analysisSettings.useAcceleration and not array.get(acceleration_valid, 0)
acc_valid_tf2 = array.get(tf_enabled, 1) and analysisSettings.useAcceleration and array.get(acceleration_valid, 1)
acc_invalid_tf2 = array.get(tf_enabled, 1) and analysisSettings.useAcceleration and not array.get(acceleration_valid, 1)
acc_valid_tf3 = array.get(tf_enabled, 2) and analysisSettings.useAcceleration and array.get(acceleration_valid, 2)
acc_invalid_tf3 = array.get(tf_enabled, 2) and analysisSettings.useAcceleration and not array.get(acceleration_valid, 2)
acc_valid_tf4 = array.get(tf_enabled, 3) and analysisSettings.useAcceleration and array.get(acceleration_valid, 3)
acc_invalid_tf4 = array.get(tf_enabled, 3) and analysisSettings.useAcceleration and not array.get(acceleration_valid, 3)

plot(indicator_tf1, "Indicator TF1", color.new(color.lime, 0), 2, plot.style_line)
plot(indicator_tf2, "Indicator TF2", color.new(color.red, 0), 2, plot.style_line)
plot(indicator_tf3, "Indicator TF3", color.new(color.orange, 0), 2, plot.style_line)
plot(indicator_tf4, "Indicator TF4", color.new(color.aqua, 0), 2, plot.style_line)

plot(compositeScore, "Composite Score", color.new(color.blue, 0), 2)
plot(directionSum, "Direction Sum", color.new(color.purple, 0), 2)
plot(validCount, "Valid Count", color.new(color.gray, 0), 2)

plot(entrySignal ? -5 : na, "Entry Signal", color.new(color.lime, 0), 4, plot.style_circles)

plot(signalDirection > 0 ? -10 : na, "Bullish", color.new(color.green, 0), 4, plot.style_circles)
plot(signalDirection < 0 ? -10 : na, "Bearish", color.new(color.red, 0), 4, plot.style_circles)

plot(dir_valid_tf1 ? tf1_base : na, "TF1 Direction Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(dir_invalid_tf1 ? tf1_base : na, "TF1 Direction Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(slope_valid_tf1 ? tf1_base - 5 : na, "TF1 Slope Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(slope_invalid_tf1 ? tf1_base - 5 : na, "TF1 Slope Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(acc_valid_tf1 ? tf1_base - 10 : na, "TF1 Acceleration Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(acc_invalid_tf1 ? tf1_base - 10 : na, "TF1 Acceleration Invalid", color.new(color.red, 0), 2, plot.style_circles)

plot(dir_valid_tf2 ? tf2_base : na, "TF2 Direction Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(dir_invalid_tf2 ? tf2_base : na, "TF2 Direction Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(slope_valid_tf2 ? tf2_base - 5 : na, "TF2 Slope Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(slope_invalid_tf2 ? tf2_base - 5 : na, "TF2 Slope Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(acc_valid_tf2 ? tf2_base - 10 : na, "TF2 Acceleration Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(acc_invalid_tf2 ? tf2_base - 10 : na, "TF2 Acceleration Invalid", color.new(color.red, 0), 2, plot.style_circles)

plot(dir_valid_tf3 ? tf3_base : na, "TF3 Direction Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(dir_invalid_tf3 ? tf3_base : na, "TF3 Direction Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(slope_valid_tf3 ? tf3_base - 5 : na, "TF3 Slope Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(slope_invalid_tf3 ? tf3_base - 5 : na, "TF3 Slope Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(acc_valid_tf3 ? tf3_base - 10 : na, "TF3 Acceleration Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(acc_invalid_tf3 ? tf3_base - 10 : na, "TF3 Acceleration Invalid", color.new(color.red, 0), 2, plot.style_circles)

plot(dir_valid_tf4 ? tf4_base : na, "TF4 Direction Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(dir_invalid_tf4 ? tf4_base : na, "TF4 Direction Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(slope_valid_tf4 ? tf4_base - 5 : na, "TF4 Slope Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(slope_invalid_tf4 ? tf4_base - 5 : na, "TF4 Slope Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(acc_valid_tf4 ? tf4_base - 10 : na, "TF4 Acceleration Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(acc_invalid_tf4 ? tf4_base - 10 : na, "TF4 Acceleration Invalid", color.new(color.red, 0), 2, plot.style_circles)
