{"name": "mtf-cmo-trading-system", "version": "1.0.0", "description": "Multi-timeframe CMO trading system with institutional risk controls", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "backtest": "ts-node src/backtest.ts", "live": "ts-node src/live.ts"}, "dependencies": {"@backtestjs/framework": "^1.0.0", "ccxt": "^4.1.0", "ws": "^8.14.0", "node-cron": "^3.0.2", "dotenv": "^16.3.1", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/ws": "^8.5.0", "typescript": "^5.2.0", "ts-node": "^10.9.0", "jest": "^29.7.0", "@types/jest": "^29.5.0"}, "keywords": ["trading", "cryptocurrency", "backtesting", "multi-timeframe", "risk-management"], "author": "Your Name", "license": "MIT"}