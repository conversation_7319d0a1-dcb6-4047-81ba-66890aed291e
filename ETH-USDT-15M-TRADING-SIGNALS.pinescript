// © cryptokairo — MPL-2.0
//@version=5
indicator("ETH/USDT 15m Automated Trading Signals", overlay=false, max_labels_count=500)

// === TRADING SETTINGS ===
trading_group = "Trading Setup"
entry_threshold   = input.float(1.2, "Entry Threshold", minval=0.5, maxval=3.0, step=0.1, group=trading_group, tooltip="Higher = fewer but stronger signals")
exit_threshold    = input.float(0.8, "Exit Threshold", minval=0.2, maxval=2.0, step=0.1, group=trading_group)
stop_loss_pct     = input.float(2.0, "Stop Loss %", minval=0.5, maxval=5.0, step=0.1, group=trading_group)
take_profit_pct   = input.float(4.0, "Take Profit %", minval=1.0, maxval=10.0, step=0.1, group=trading_group)

// === INDICATOR SETTINGS ===
indicator_group = "Indicators"
rsi_length        = input.int(14, "RSI Length", group=indicator_group)
ema_fast          = input.int(12, "EMA Fast", group=indicator_group)
ema_slow          = input.int(26, "EMA Slow", group=indicator_group)
adx_length        = input.int(14, "ADX Length", group=indicator_group)
volume_ma_length  = input.int(20, "Volume MA Length", group=indicator_group)

// === ALERT SETTINGS ===
alert_group = "Alerts"
enable_alerts     = input.bool(true, "Enable Alerts", group=alert_group)
webhook_enabled   = input.bool(false, "Webhook Ready", group=alert_group, tooltip="Format alerts for trading bot webhooks")

// === SIMPLE TRADING INDICATORS ===

// RSI with clear signals
rsi = ta.rsi(close, rsi_length)
rsi_oversold = rsi < 30
rsi_overbought = rsi > 70
rsi_bullish = rsi > 50 and rsi[1] <= 50
rsi_bearish = rsi < 50 and rsi[1] >= 50

// EMA trend
ema_fast_line = ta.ema(close, ema_fast)
ema_slow_line = ta.ema(close, ema_slow)
ema_bullish = ema_fast_line > ema_slow_line and ema_fast_line > ema_fast_line[1]
ema_bearish = ema_fast_line < ema_slow_line and ema_fast_line < ema_fast_line[1]

// ADX trend strength
[di_plus, di_minus, adx] = ta.dmi(adx_length, adx_length)
trend_strong = adx > 25
trend_bullish = di_plus > di_minus and trend_strong
trend_bearish = di_minus > di_plus and trend_strong

// Volume confirmation
volume_ma = ta.sma(volume, volume_ma_length)
volume_high = volume > volume_ma * 1.5
volume_normal = volume > volume_ma

// Price momentum
price_momentum = (close - close[5]) / close[5] * 100
momentum_bullish = price_momentum > 0.5
momentum_bearish = price_momentum < -0.5

// === SIGNAL SCORING SYSTEM ===

// Calculate bullish score (0-6 points)
bullish_score = 0
bullish_score := bullish_score + (ema_bullish ? 1 : 0)
bullish_score := bullish_score + (trend_bullish ? 1 : 0)
bullish_score := bullish_score + (rsi_bullish ? 1 : 0)
bullish_score := bullish_score + (momentum_bullish ? 1 : 0)
bullish_score := bullish_score + (volume_normal ? 1 : 0)
bullish_score := bullish_score + (volume_high ? 1 : 0)

// Calculate bearish score (0-6 points)
bearish_score = 0
bearish_score := bearish_score + (ema_bearish ? 1 : 0)
bearish_score := bearish_score + (trend_bearish ? 1 : 0)
bearish_score := bearish_score + (rsi_bearish ? 1 : 0)
bearish_score := bearish_score + (momentum_bearish ? 1 : 0)
bearish_score := bearish_score + (volume_normal ? 1 : 0)
bearish_score := bearish_score + (volume_high ? 1 : 0)

// === ENTRY/EXIT SIGNALS ===

// Entry signals
entry_long = bullish_score >= entry_threshold and not rsi_overbought
entry_short = bearish_score >= entry_threshold and not rsi_oversold

// Exit signals
exit_long = bullish_score < exit_threshold or rsi_overbought
exit_short = bearish_score < exit_threshold or rsi_oversold

// Signal strength
signal_strength = math.max(bullish_score, bearish_score)
signal_direction = bullish_score > bearish_score ? 1 : bearish_score > bullish_score ? -1 : 0

// === POSITION TRACKING ===
var bool in_long_position = false
var bool in_short_position = false
var float entry_price = na
var float stop_loss_price = na
var float take_profit_price = na

// Position management
if entry_long and not in_long_position and not in_short_position
    in_long_position := true
    in_short_position := false
    entry_price := close
    stop_loss_price := close * (1 - stop_loss_pct / 100)
    take_profit_price := close * (1 + take_profit_pct / 100)

if entry_short and not in_short_position and not in_long_position
    in_short_position := true
    in_long_position := false
    entry_price := close
    stop_loss_price := close * (1 + stop_loss_pct / 100)
    take_profit_price := close * (1 - take_profit_pct / 100)

// Exit conditions
exit_long_position = in_long_position and (exit_long or close <= stop_loss_price or close >= take_profit_price)
exit_short_position = in_short_position and (exit_short or close >= stop_loss_price or close <= take_profit_price)

if exit_long_position
    in_long_position := false
    entry_price := na
    stop_loss_price := na
    take_profit_price := na

if exit_short_position
    in_short_position := false
    entry_price := na
    stop_loss_price := na
    take_profit_price := na

// === ALERTS ===
if enable_alerts
    // Entry alerts
    if entry_long and not in_long_position[1]
        alert_msg = webhook_enabled ? 
         '{"action":"buy","symbol":"ETHUSDT","price":' + str.tostring(close) + ',"sl":' + str.tostring(stop_loss_price) + ',"tp":' + str.tostring(take_profit_price) + '}' :
         "LONG ENTRY: ETH/USDT at " + str.tostring(close) + " | SL: " + str.tostring(stop_loss_price) + " | TP: " + str.tostring(take_profit_price) + " | Score: " + str.tostring(bullish_score)
        alert(alert_msg, alert.freq_once_per_bar)
    
    if entry_short and not in_short_position[1]
        alert_msg = webhook_enabled ? 
         '{"action":"sell","symbol":"ETHUSDT","price":' + str.tostring(close) + ',"sl":' + str.tostring(stop_loss_price) + ',"tp":' + str.tostring(take_profit_price) + '}' :
         "SHORT ENTRY: ETH/USDT at " + str.tostring(close) + " | SL: " + str.tostring(stop_loss_price) + " | TP: " + str.tostring(take_profit_price) + " | Score: " + str.tostring(bearish_score)
        alert(alert_msg, alert.freq_once_per_bar)
    
    // Exit alerts
    if exit_long_position
        exit_reason = close <= stop_loss_price ? "STOP LOSS" : close >= take_profit_price ? "TAKE PROFIT" : "SIGNAL EXIT"
        alert_msg = webhook_enabled ? 
         '{"action":"close","symbol":"ETHUSDT","side":"long"}' :
         "EXIT LONG: " + exit_reason + " at " + str.tostring(close)
        alert(alert_msg, alert.freq_once_per_bar)
    
    if exit_short_position
        exit_reason = close >= stop_loss_price ? "STOP LOSS" : close <= take_profit_price ? "TAKE PROFIT" : "SIGNAL EXIT"
        alert_msg = webhook_enabled ? 
         '{"action":"close","symbol":"ETHUSDT","side":"short"}' :
         "EXIT SHORT: " + exit_reason + " at " + str.tostring(close)
        alert(alert_msg, alert.freq_once_per_bar)

// === PLOTTING ===

// Signal scores
plot(bullish_score, "Bullish Score", color=color.new(color.green, 0), linewidth=2)
plot(bearish_score, "Bearish Score", color=color.new(color.red, 0), linewidth=2)
plot(signal_strength, "Signal Strength", color=color.new(color.white, 0), linewidth=1)

// Entry signals
plot(entry_long ? 5 : na, "Long Entry", color=color.new(color.lime, 0), style=plot.style_circles, linewidth=4)
plot(entry_short ? -5 : na, "Short Entry", color=color.new(color.red, 0), style=plot.style_circles, linewidth=4)

// Exit signals
plot(exit_long_position ? 4 : na, "Exit Long", color=color.new(color.orange, 0), style=plot.style_cross, linewidth=3)
plot(exit_short_position ? -4 : na, "Exit Short", color=color.new(color.orange, 0), style=plot.style_cross, linewidth=3)

// Position status
bgcolor(in_long_position ? color.new(color.green, 90) : in_short_position ? color.new(color.red, 90) : na)

// Threshold lines
hline(entry_threshold, "Entry Threshold", color=color.yellow, linestyle=hline.style_dashed)
hline(exit_threshold, "Exit Threshold", color=color.orange, linestyle=hline.style_dashed)
hline(0, "Zero Line", color=color.white)

// RSI levels for reference
plot(rsi/10 - 3, "RSI/10", color=color.new(color.blue, 50), linewidth=1)
hline(-2, "RSI 30", color=color.blue, linestyle=hline.style_dotted)
hline(-2, "RSI 70", color=color.blue, linestyle=hline.style_dotted)
