// This Pine Script® code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © cryptokairo
//@version=5
indicator("MTF Generic Analyzer", overlay=false)

// ===========================
// ==== Constants & Colors ===
// ===========================

COLOR_GRAY           = color.rgb(87, 89, 94)
COLOR_BLUE           = color.rgb(64, 118, 179)
COLOR_TREND_BULL     = color.rgb(16 , 228, 217)
COLOR_TREND_BEAR     = color.rgb(201, 206, 63)
COLOR_SUPPLY         = color.rgb(170, 0  , 10)
COLOR_DEMAND         = color.rgb(0  , 190, 120)
COLOR_SLOPE_UP       = color.rgb(147, 212, 68)
COLOR_SLOPE_DOWN     = color.rgb(206, 60 , 140)
COLOR_SLOPE_NEUTRAL  = color.rgb(165, 165, 165)
COLOR_CMO_M1         = color.rgb(76, 175, 79, 50)
COLOR_CMO_M2         = color.rgb(255, 235, 59, 50)
COLOR_CMO_M3         = color.rgb(255, 152, 0, 50)
COLOR_CM0_M0         = color.rgb(255, 82, 82, 50)

// ===========================
// ==== Type Definitions =====
// ===========================

// Type for indicator settings
type IndicatorSettings
    string name
    string type
    float source
    int period
    int smoothing
    float threshold

// Type for analysis component settings
type AnalysisSettings
    bool useDirection
    bool useSlope
    bool useAcceleration
    float directionThreshold
    float slopeThreshold
    float accelerationThreshold

// Type for timeframe settings
type TimeframeSettings
    string timeframe
    float weight
    bool enabled

// Type for indicator results
type IndicatorResult
    float value
    float normalized
    float direction
    float slope
    float acceleration
    bool directionValid
    bool slopeValid
    bool accelerationValid

// Type for timeframe results
type TimeframeResult
    IndicatorResult[] indicatorResults
    float compositeScore
    int directionSum
    bool isValid

// ===========================
// ==== Helper Functions =====
// ===========================

// Normalize an indicator value to a standard range (-1 to 1)
normalizeIndicator(float value, string type, float min, float max) =>
    result = 0.0
    if type == "ADX"
        // ADX is 0 to 100, normalize to 0 to 1
        result := value / 100
    else if type == "RF"
        // Range Filter is relative to price, normalize based on ATR
        atr = ta.atr(14)
        result := value / (atr * 10)
    else
        // Default normalization using min-max scaling
        range = max - min
        result := range != 0 ? (value - min) / range * 2 - 1 : 0

    // Ensure result is between -1 and 1
    math.max(-1, math.min(1, result))

// Calculate direction of an indicator
analyzeDirection(float current, float previous, float threshold) =>
    direction = current > previous ? 1 : current < previous ? -1 : 0
    isValid = math.abs(current - previous) >= threshold
    [direction, isValid]

// Calculate slope of an indicator
analyzeSlope(float[] values, int length, float threshold) =>
    if array.size(values) < length
        [0.0, false]
    else
        // Calculate linear regression slope
        sumX = 0.0
        sumY = 0.0
        sumXY = 0.0
        sumX2 = 0.0

        for i = 0 to length - 1
            x = i
            y = array.get(values, array.size(values) - 1 - i)
            sumX := sumX + x
            sumY := sumY + y
            sumXY := sumXY + x * y
            sumX2 := sumX2 + x * x

        n = length
        slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX)
        isValid = math.abs(slope) >= threshold

        [slope, isValid]

// Calculate acceleration of an indicator
analyzeAcceleration(float[] slopes, int length, float threshold) =>
    if array.size(slopes) < length
        [0.0, false]
    else
        // Calculate change in slope (acceleration)
        current = array.get(slopes, array.size(slopes) - 1)
        previous = array.get(slopes, array.size(slopes) - 2)
        acceleration = current - previous
        isValid = math.abs(acceleration) >= threshold

        [acceleration, isValid]

// Calculate Z-score
f_zscore(float _src, int _length) =>
    _mean = ta.sma(_src, _length)
    _std = ta.stdev(_src - _mean, _length)
    _value = _std != 0 ? (_src - _mean) / _std : 0
    _value

// Calculate ADX
calcADX(int len) =>
    tr = math.max(math.max(high - low, math.abs(high - nz(close[1]))), math.abs(low - nz(close[1])))
    upMove = high - nz(high[1])
    downMove = nz(low[1]) - low
    plusDM = upMove > downMove and upMove > 0 ? upMove : 0
    minusDM = downMove > upMove and downMove > 0 ? downMove : 0
    trSum = ta.rma(tr, len)
    plusDISum = ta.rma(plusDM, len)
    minusDISum = ta.rma(minusDM, len)
    plusDI = trSum != 0 ? 100 * plusDISum / trSum : 0
    minusDI = trSum != 0 ? 100 * minusDISum / trSum : 0
    dx = (plusDI + minusDI) != 0 ? 100 * math.abs(plusDI - minusDI) / (plusDI + minusDI) : 0
    adx = ta.rma(dx, len)
    [adx, plusDI, minusDI]

// Calculate Range Filter
calcRangeFilter(float src, float smrng, float filtPrev) =>
    filtNew = src > nz(filtPrev) ? (src - smrng < nz(filtPrev) ? nz(filtPrev) : src - smrng) : src + smrng > nz(filtPrev) ? nz(filtPrev) : src + smrng
    filtNew

// ===========================
// ==== Input Definitions ====
// ===========================

// General settings
general_group = "General Settings"
src = input(close, "Source", group=general_group)

// Timeframe settings using the "Features" pattern
timeframe_group = "Timeframe Settings"

// Define default values for timeframes
var string[] tf_names = array.new_string(4)
array.set(tf_names, 0, "TF1")
array.set(tf_names, 1, "TF2")
array.set(tf_names, 2, "TF3")
array.set(tf_names, 3, "TF4")

var string[] tf_periods = array.new_string(4)
array.set(tf_periods, 0, "15")
array.set(tf_periods, 1, "30")
array.set(tf_periods, 2, "45")
array.set(tf_periods, 3, "60")

var float[] tf_default_weights = array.new_float(4)
array.set(tf_default_weights, 0, 1.0)
array.set(tf_default_weights, 1, 0.75)
array.set(tf_default_weights, 2, 0.5)
array.set(tf_default_weights, 3, 0.25)

// Timeframe selection
tf_selector = input.int(1, "Timeframe to Configure", minval=1, maxval=4, group=timeframe_group)

// Timeframe enable inputs
tf_enabled_1 = input.bool(true, "Enable TF1", group=timeframe_group, inline="enable")
tf_enabled_2 = input.bool(true, "Enable TF2", group=timeframe_group, inline="enable")
tf_enabled_3 = input.bool(true, "Enable TF3", group=timeframe_group, inline="enable")
tf_enabled_4 = input.bool(true, "Enable TF4", group=timeframe_group, inline="enable")

// Timeframe settings - using const values instead of series
tf1 = input.timeframe("15", "TF1 Timeframe", group=timeframe_group)
tf2 = input.timeframe("30", "TF2 Timeframe", group=timeframe_group)
tf3 = input.timeframe("45", "TF3 Timeframe", group=timeframe_group)
tf4 = input.timeframe("60", "TF4 Timeframe", group=timeframe_group)

tf_weight_1 = input.float(1.0, "TF1 Weight", group=timeframe_group)
tf_weight_2 = input.float(0.75, "TF2 Weight", group=timeframe_group)
tf_weight_3 = input.float(0.5, "TF3 Weight", group=timeframe_group)
tf_weight_4 = input.float(0.25, "TF4 Weight", group=timeframe_group)

// Analysis components settings
analysis_group = "Analysis Components"
use_direction = input.bool(true, "Use Direction Analysis", group=analysis_group)
use_slope = input.bool(true, "Use Slope Analysis", group=analysis_group)
use_acceleration = input.bool(true, "Use Acceleration Analysis", group=analysis_group)
direction_threshold = input.float(0.01, "Direction Threshold", minval=0.001, group=analysis_group)
slope_threshold = input.float(0.05, "Slope Threshold", minval=0.001, group=analysis_group)
acceleration_threshold = input.float(0.01, "Acceleration Threshold", minval=0.001, group=analysis_group)

// Indicator settings
indicator_group = "Indicator Settings"
indicator_selector = input.string("ADX", "Indicator Type", options=["ADX", "RF"], group=indicator_group)
indicator_period = input.int(14, "Period", minval=1, group=indicator_group)
indicator_smoothing = input.int(14, "Smoothing", minval=1, group=indicator_group)
indicator_threshold = input.float(20, "Threshold", minval=0, group=indicator_group)

// Composite settings
composite_group = "Composite Settings"
min_valid_timeframes = input.int(2, "Min Valid Timeframes", minval=1, maxval=4, group=composite_group)
min_direction_agreement = input.int(3, "Min Direction Agreement", minval=1, maxval=4, group=composite_group)
min_composite_score = input.float(4.0, "Min Composite Score", step=0.1, group=composite_group)

// ===========================
// ==== Arrays & Storage =====
// ===========================

// Store timeframe settings in arrays
var string[] tf_timeframes = array.new_string(4)
var float[] tf_weights = array.new_float(4)
var bool[] tf_enabled = array.new_bool(4)

// Initialize arrays on first bar
if barstate.isfirst
    // Set default timeframes
    array.set(tf_timeframes, 0, tf1)
    array.set(tf_timeframes, 1, tf2)
    array.set(tf_timeframes, 2, tf3)
    array.set(tf_timeframes, 3, tf4)

    // Set default weights
    array.set(tf_weights, 0, tf_weight_1)
    array.set(tf_weights, 1, tf_weight_2)
    array.set(tf_weights, 2, tf_weight_3)
    array.set(tf_weights, 3, tf_weight_4)

    // Set default enabled states
    array.set(tf_enabled, 0, tf_enabled_1)
    array.set(tf_enabled, 1, tf_enabled_2)
    array.set(tf_enabled, 2, tf_enabled_3)
    array.set(tf_enabled, 3, tf_enabled_4)

// Update enabled flags on each bar
array.set(tf_enabled, 0, tf_enabled_1)
array.set(tf_enabled, 1, tf_enabled_2)
array.set(tf_enabled, 2, tf_enabled_3)
array.set(tf_enabled, 3, tf_enabled_4)

// Create analysis settings object
AnalysisSettings analysisSettings = AnalysisSettings.new(
    use_direction,
    use_slope,
    use_acceleration,
    direction_threshold,
    slope_threshold,
    acceleration_threshold
)

// Create indicator settings object
IndicatorSettings indicatorSettings = IndicatorSettings.new(
    "Generic Indicator",
    indicator_selector,
    src,
    indicator_period,
    indicator_smoothing,
    indicator_threshold
)

// ===========================
// ==== Processing Logic =====
// ===========================

// Arrays to store historical values for slope and acceleration calculation
var float[][] indicatorValues = array.new_array(4)
var float[][] indicatorSlopes = array.new_array(4)

// Initialize arrays on first bar
if barstate.isfirst
    for i = 0 to 3
        array.set(indicatorValues, i, array.new_float())
        array.set(indicatorSlopes, i, array.new_float())

// Process a single indicator for a specific timeframe
processIndicator(string timeframe, string indicatorType, float src, int period, int smoothing) =>
    result = 0.0

    if indicatorType == "ADX"
        [adx, plusDI, minusDI] = request.security(syminfo.tickerid, timeframe, calcADX(period))
        result := adx
    else if indicatorType == "RF"
        // Calculate Range Filter
        avrng = request.security(syminfo.tickerid, timeframe, ta.ema(math.abs(src - src[1]), smoothing))
        smrng = request.security(syminfo.tickerid, timeframe, ta.ema(avrng, smoothing * 2 - 1))

        // Store previous filter value
        var float prevFilter = na

        // Calculate new filter value
        newFilter = request.security(syminfo.tickerid, timeframe, calcRangeFilter(src, smrng, prevFilter))
        prevFilter := newFilter

        result := newFilter

    result

// Analyze an indicator value
analyzeIndicatorValue(float value, float[] historicalValues, float[] historicalSlopes, AnalysisSettings settings) =>
    // Store the current value in historical array
    array.push(historicalValues, value)

    // Keep only the last 50 values
    if array.size(historicalValues) > 50
        array.shift(historicalValues)

    // Calculate normalized value
    normalized = normalizeIndicator(value, indicatorSettings.type, 0, 100)

    // Calculate direction
    prevValue = array.size(historicalValues) > 1 ? array.get(historicalValues, array.size(historicalValues) - 2) : value
    [direction, directionValid] = analyzeDirection(value, prevValue, settings.directionThreshold)

    // Calculate slope
    [slope, slopeValid] = analyzeSlope(historicalValues, math.min(10, array.size(historicalValues)), settings.slopeThreshold)

    // Store the current slope in historical slopes array
    array.push(historicalSlopes, slope)

    // Keep only the last 20 slopes
    if array.size(historicalSlopes) > 20
        array.shift(historicalSlopes)

    // Calculate acceleration
    [acceleration, accelerationValid] = analyzeAcceleration(historicalSlopes, math.min(5, array.size(historicalSlopes)), settings.accelerationThreshold)

    // Create and return indicator result
    IndicatorResult.new(
        value,
        normalized,
        direction,
        slope,
        acceleration,
        directionValid,
        slopeValid,
        accelerationValid
    )

// Process all timeframes
processTimeframes() =>
    var TimeframeResult[] results = array.new_array(4)

    // Process each timeframe
    for i = 0 to 3
        if array.get(tf_enabled, i)
            // Get timeframe settings
            timeframe = array.get(tf_timeframes, i)

            // Process indicator
            indicatorValue = processIndicator(timeframe, indicatorSettings.type, indicatorSettings.source, indicatorSettings.period, indicatorSettings.smoothing)

            // Analyze indicator
            indicatorResult = analyzeIndicatorValue(indicatorValue, array.get(indicatorValues, i), array.get(indicatorSlopes, i), analysisSettings)

            // Create indicator results array
            indicatorResults = array.new_array(1)
            array.set(indicatorResults, 0, indicatorResult)

            // Calculate composite score and direction sum
            compositeScore = 0.0
            directionSum = 0

            // Only consider valid components based on settings
            if analysisSettings.useDirection and indicatorResult.directionValid
                compositeScore += math.abs(indicatorResult.direction) * array.get(tf_weights, i)
                directionSum += indicatorResult.direction

            if analysisSettings.useSlope and indicatorResult.slopeValid
                compositeScore += math.abs(indicatorResult.slope) * array.get(tf_weights, i)

            if analysisSettings.useAcceleration and indicatorResult.accelerationValid
                compositeScore += math.abs(indicatorResult.acceleration) * array.get(tf_weights, i)

            // Determine if timeframe is valid
            isValid = (not analysisSettings.useDirection or indicatorResult.directionValid) and
                     (not analysisSettings.useSlope or indicatorResult.slopeValid) and
                     (not analysisSettings.useAcceleration or indicatorResult.accelerationValid)

            // Create timeframe result
            timeframeResult = TimeframeResult.new(
                indicatorResults,
                compositeScore,
                directionSum,
                isValid
            )

            // Store result
            array.set(results, i, timeframeResult)

    results

// Generate final signal
generateSignal(TimeframeResult[] results) =>
    validCount = 0
    totalDirectionSum = 0
    totalCompositeScore = 0.0

    for i = 0 to 3
        if array.get(tf_enabled, i)
            result = array.get(results, i)
            if result != na and result.isValid
                validCount += 1
                totalDirectionSum += result.directionSum
                totalCompositeScore += result.compositeScore

    isValid = validCount >= min_valid_timeframes
    hasDirection = math.abs(totalDirectionSum) >= min_direction_agreement
    hasStrength = totalCompositeScore >= min_composite_score

    signal = isValid and hasDirection and hasStrength
    direction = totalDirectionSum > 0 ? 1 : totalDirectionSum < 0 ? -1 : 0

    [signal, direction, validCount, totalDirectionSum, totalCompositeScore]

// ===========================
// ==== Main Calculation =====
// ===========================

// Process all timeframes
timeframeResults = processTimeframes()

// Generate final signal
[entrySignal, signalDirection, validCount, directionSum, compositeScore] = generateSignal(timeframeResults)

// ===========================
// ======= Plotting ==========
// ===========================

// Pre-calculate indicator values and validity flags for plotting
var float[] indicator_values = array.new_float(4, na)
var bool[] direction_valid = array.new_bool(4, false)
var bool[] slope_valid = array.new_bool(4, false)
var bool[] acceleration_valid = array.new_bool(4, false)

// Update values for plotting
for i = 0 to 3
    if array.get(tf_enabled, i)
        result = array.get(timeframeResults, i)
        if result != na
            indicatorResult = array.get(result.indicatorResults, 0)
            array.set(indicator_values, i, indicatorResult.value)
            array.set(direction_valid, i, indicatorResult.directionValid)
            array.set(slope_valid, i, indicatorResult.slopeValid)
            array.set(acceleration_valid, i, indicatorResult.accelerationValid)

// Define base positions for plotting
var float tf1_base = -20
var float tf2_base = -35
var float tf3_base = -50
var float tf4_base = -65

// Plot indicator values for each timeframe (at global scope)
plot(array.get(tf_enabled, 0) ? array.get(indicator_values, 0) : na, "Indicator TF1", color.new(color.lime, 0), 2, plot.style_line)
plot(array.get(tf_enabled, 1) ? array.get(indicator_values, 1) : na, "Indicator TF2", color.new(color.red, 0), 2, plot.style_line)
plot(array.get(tf_enabled, 2) ? array.get(indicator_values, 2) : na, "Indicator TF3", color.new(color.orange, 0), 2, plot.style_line)
plot(array.get(tf_enabled, 3) ? array.get(indicator_values, 3) : na, "Indicator TF4", color.new(color.aqua, 0), 2, plot.style_line)

// Plot composite metrics
plot(compositeScore, "Composite Score", color.new(color.blue, 0), 2)
plot(directionSum, "Direction Sum", color.new(color.purple, 0), 2)
plot(validCount, "Valid Count", color.new(color.gray, 0), 2)

// Plot entry signal
plot(entrySignal ? -5 : na, "Entry Signal", color.new(color.lime, 0), 4, plot.style_circles)

// Plot direction
plot(signalDirection > 0 ? -10 : na, "Bullish", color.new(color.green, 0), 4, plot.style_circles)
plot(signalDirection < 0 ? -10 : na, "Bearish", color.new(color.red, 0), 4, plot.style_circles)

// Plot analysis components for TF1 (at global scope)
plot(array.get(tf_enabled, 0) and analysisSettings.useDirection and array.get(direction_valid, 0) ? tf1_base : na,
     "TF1 Direction Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 0) and analysisSettings.useDirection and not array.get(direction_valid, 0) ? tf1_base : na,
     "TF1 Direction Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 0) and analysisSettings.useSlope and array.get(slope_valid, 0) ? tf1_base - 5 : na,
     "TF1 Slope Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 0) and analysisSettings.useSlope and not array.get(slope_valid, 0) ? tf1_base - 5 : na,
     "TF1 Slope Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 0) and analysisSettings.useAcceleration and array.get(acceleration_valid, 0) ? tf1_base - 10 : na,
     "TF1 Acceleration Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 0) and analysisSettings.useAcceleration and not array.get(acceleration_valid, 0) ? tf1_base - 10 : na,
     "TF1 Acceleration Invalid", color.new(color.red, 0), 2, plot.style_circles)

// Plot analysis components for TF2 (at global scope)
plot(array.get(tf_enabled, 1) and analysisSettings.useDirection and array.get(direction_valid, 1) ? tf2_base : na,
     "TF2 Direction Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 1) and analysisSettings.useDirection and not array.get(direction_valid, 1) ? tf2_base : na,
     "TF2 Direction Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 1) and analysisSettings.useSlope and array.get(slope_valid, 1) ? tf2_base - 5 : na,
     "TF2 Slope Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 1) and analysisSettings.useSlope and not array.get(slope_valid, 1) ? tf2_base - 5 : na,
     "TF2 Slope Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 1) and analysisSettings.useAcceleration and array.get(acceleration_valid, 1) ? tf2_base - 10 : na,
     "TF2 Acceleration Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 1) and analysisSettings.useAcceleration and not array.get(acceleration_valid, 1) ? tf2_base - 10 : na,
     "TF2 Acceleration Invalid", color.new(color.red, 0), 2, plot.style_circles)

// Plot analysis components for TF3 (at global scope)
plot(array.get(tf_enabled, 2) and analysisSettings.useDirection and array.get(direction_valid, 2) ? tf3_base : na,
     "TF3 Direction Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 2) and analysisSettings.useDirection and not array.get(direction_valid, 2) ? tf3_base : na,
     "TF3 Direction Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 2) and analysisSettings.useSlope and array.get(slope_valid, 2) ? tf3_base - 5 : na,
     "TF3 Slope Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 2) and analysisSettings.useSlope and not array.get(slope_valid, 2) ? tf3_base - 5 : na,
     "TF3 Slope Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 2) and analysisSettings.useAcceleration and array.get(acceleration_valid, 2) ? tf3_base - 10 : na,
     "TF3 Acceleration Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 2) and analysisSettings.useAcceleration and not array.get(acceleration_valid, 2) ? tf3_base - 10 : na,
     "TF3 Acceleration Invalid", color.new(color.red, 0), 2, plot.style_circles)

// Plot analysis components for TF4 (at global scope)
plot(array.get(tf_enabled, 3) and analysisSettings.useDirection and array.get(direction_valid, 3) ? tf4_base : na,
     "TF4 Direction Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 3) and analysisSettings.useDirection and not array.get(direction_valid, 3) ? tf4_base : na,
     "TF4 Direction Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 3) and analysisSettings.useSlope and array.get(slope_valid, 3) ? tf4_base - 5 : na,
     "TF4 Slope Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 3) and analysisSettings.useSlope and not array.get(slope_valid, 3) ? tf4_base - 5 : na,
     "TF4 Slope Invalid", color.new(color.red, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 3) and analysisSettings.useAcceleration and array.get(acceleration_valid, 3) ? tf4_base - 10 : na,
     "TF4 Acceleration Valid", color.new(color.green, 0), 2, plot.style_circles)
plot(array.get(tf_enabled, 3) and analysisSettings.useAcceleration and not array.get(acceleration_valid, 3) ? tf4_base - 10 : na,
     "TF4 Acceleration Invalid", color.new(color.red, 0), 2, plot.style_circles)