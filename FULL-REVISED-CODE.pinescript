// © cryptokairo — MPL-2.0
//@version=5
indicator("S&K ADX/RF MTF Dynamic Z-Score + Automated Trading", overlay=false, max_labels_count=500)

// === CORE INDICATOR SETTINGS ===
rfLen       = input.int(14, "RF Length", group="Range Filter")
rfMultMin   = input.float(3.0, "RF Min Mult", group="Range Filter")
rfMultMax   = input.float(6.0, "RF Max Mult", group="Range Filter")
rfDynWin    = input.int(54, "RF Dynamic Multi Z Win", group="Range Filter")
adxLenDI    = input.int(54, "ADX DI Length", group="ADX")
adxLen      = input.int(17, "ADX Smoothing", group="ADX")
win         = input.int(90, "Rolling Z Window", minval=20, maxval=500, group="Analysis")
gauss_len   = input.int(7, "Gaussian Filter Length", group="Analysis")
gauss_sig   = input.float(2, "Gaussian Filter Sigma", group="Analysis")

// === TIMEFRAME SETTINGS ===
tf1         = input.timeframe("15","TF-1", group="Timeframes")
tf2         = input.timeframe("30","TF-2", group="Timeframes")
tf3         = input.timeframe("45","TF-3", group="Timeframes")
tf4         = input.timeframe("60","TF-4", group="Timeframes")

// === AUTOMATED TRADING SETTINGS ===
trading_group = "Automated Trading"
enable_trading    = input.bool(true, "Enable Trading Signals", group=trading_group)
entry_threshold   = input.float(1.5, "Entry Signal Threshold", minval=0.5, maxval=3.0, step=0.1, group=trading_group)
exit_threshold    = input.float(0.8, "Exit Signal Threshold", minval=0.2, maxval=2.0, step=0.1, group=trading_group)
quality_threshold = input.float(0.6, "Min Signal Quality", minval=0.1, maxval=1.0, step=0.1, group=trading_group)
stop_loss_pct     = input.float(2.5, "Stop Loss %", minval=0.5, maxval=5.0, step=0.1, group=trading_group)
take_profit_pct   = input.float(4.0, "Take Profit %", minval=1.0, maxval=10.0, step=0.1, group=trading_group)
max_bars_in_trade = input.int(20, "Max Bars in Trade", minval=5, maxval=50, group=trading_group)

// === ALERT SETTINGS ===
alert_group = "Alerts"
enable_alerts     = input.bool(true, "Enable Alerts", group=alert_group)
webhook_enabled   = input.bool(false, "Webhook Ready", group=alert_group, tooltip="Format alerts for trading bot webhooks")

zscore(src, len) =>
    mu  = ta.sma(src, len)
    sig = ta.stdev(src, len)
    sig == 0 ? na : (src - mu) / sig

dyn_mult(z, minMult, maxMult) =>
    scale = math.min(math.abs(z) / 2.5, 1)
    minMult + (maxMult - minMult) * scale

rf_htf(len, minMult, maxMult, dynWin, stdWin) =>
    cmo   = ta.cmo(close, len)
    cmoZ  = zscore(cmo, dynWin)
    mult  = dyn_mult(cmoZ, minMult, maxMult)
    width = ta.stdev(close - close[1], stdWin) * mult
    var float rf_state = na
    rf_state := na(rf_state) ? close : rf_state
    rf_state := close > rf_state ? math.max(rf_state, close - width) : close < rf_state ? math.min(rf_state, close + width) : rf_state
    rf_state

adx_htf(lenDI, lenADX) =>
    [_, _, adxVal] = ta.dmi(lenDI, lenADX)
    adxVal

rolling_z(val, arr, win) =>
    sz = array.size(arr)
    if sz < win or na(val) or sz == 0
        na
    else
        mu = 0.0
        for i = math.max(0, sz - win) to sz - 1
            if i >= 0 and i < sz
                mu += array.get(arr, i)
        mu /= math.min(win, sz)
        sig = 0.0
        for i = math.max(0, sz - win) to sz - 1
            if i >= 0 and i < sz
                sig += math.pow(array.get(arr, i) - mu, 2)
        sig := math.sqrt(sig / math.min(win, sz))
        sig == 0 ? na : (val - mu) / sig

gaussian_filter(arr, length, sigma) =>
    sz = array.size(arr)
    if sz < length
        na
    else
        sum   = 0.0
        total = 0.0
        pi = math.pi
        for i = 0 to length - 1
            weight = math.exp(-0.5 * math.pow((i - length/2) / sigma, 2.0)) / math.sqrt(sigma * 2.0 * pi)
            val = array.get(arr, sz - 1 - i)
            if not na(val)
                sum   += val * weight
                total += weight
        total == 0 ? na : sum / total

get_last(arr) =>
    array.size(arr) > 0 ? array.get(arr, array.size(arr) - 1) : na

norm_vol(vol, tf) =>
    minv = request.security(syminfo.tickerid, tf, ta.lowest(volume, win))
    maxv = request.security(syminfo.tickerid, tf, ta.highest(volume, win))
    (vol - minv) / (maxv - minv + 1e-9)
    
composite(a, b, c, d, w1, w2, w3, w4) =>
    num = nz(a) * w1 + nz(b) * w2 + nz(c) * w3 + nz(d) * w4
    den = nz(w1) + nz(w2) + nz(w3) + nz(w4)
    den != 0 ? num / den : na

tanh(series float _src) =>
    -1 + 2/(1 + math.exp(-2*_src))

tanh_norm(src, len) =>
    diff = src - src[1]
    rms = ta.stdev(diff, len) + 1e-9
    tanh(diff / rms)

normalizeDeriv(_src, _quadraticMeanLength) =>
    derivative = _src - _src[2]
    qMean = math.sqrt(nz(math.sum(math.pow(derivative, 2), _quadraticMeanLength)) / _quadraticMeanLength)
    qMean != 0 ? derivative / qMean : na



type Metrics
    float[] vals
    float[] slopes
    float[] accels
    float lastVal
    float lastSlope
    float lastAccel

newMetrics() =>
   Metrics.new(
       array.new<float>(), 
       array.new<float>(), 
       array.new<float>(), 
       na, na, na
     )

f_pushShift(arr, val, len) =>
    array.push(arr, val)
    if array.size(arr) > len
        array.shift(arr)

f_process(tf, func, len, met) =>
    val = request.security(syminfo.tickerid, tf, func)
    f_pushShift(met.vals, val, len)
    sz = array.size(met.vals)
    sz_slope = array.size(met.slopes)

    // Calculate slope with proper bounds checking
    slope = sz >= 2 ? normalizeDeriv(array.get(met.vals, sz - 1), len) : na

    // Calculate acceleration with proper bounds checking
    accel = sz_slope >= 2 ? normalizeDeriv(array.get(met.slopes, sz_slope - 1), len) : na

    // Push slope and acceleration (na values are handled by rolling_z later)
    f_pushShift(met.slopes, slope, len)
    f_pushShift(met.accels, accel, len)

    met.lastVal   := val
    met.lastSlope := slope
    met.lastAccel := accel

var Metrics rf_1   = newMetrics()
var Metrics rf_3   = newMetrics()
var Metrics rf_5   = newMetrics()
var Metrics rf_15  = newMetrics()

var Metrics adx_1  = newMetrics()
var Metrics adx_3  = newMetrics()
var Metrics adx_5  = newMetrics()
var Metrics adx_15 = newMetrics()

var Metrics vol_1  = newMetrics()
var Metrics vol_3  = newMetrics()
var Metrics vol_5  = newMetrics()
var Metrics vol_15 = newMetrics()

var Metrics osc_1  = newMetrics()
var Metrics osc_3  = newMetrics()
var Metrics osc_5  = newMetrics()
var Metrics osc_15 = newMetrics()

var float[] comp_s_rf   = array.new_float()
var float[] comp_a_rf   = array.new_float()
var float[] comp_s_adx  = array.new_float()
var float[] comp_a_adx  = array.new_float()

tfs = array.from(tf1, tf2, tf3, tf4)
rf_metrics  = array.from(rf_1, rf_3, rf_5, rf_15)
adx_metrics = array.from(adx_1, adx_3, adx_5, adx_15)
vol_metrics = array.from(vol_1, vol_3, vol_5, vol_15)
osc_metrics = array.from(osc_1, osc_3, osc_5, osc_15)

w1  = vol_1.lastVal
w3  = vol_3.lastVal
w5  = vol_5.lastVal
w15 = vol_15.lastVal

f_process(tf1, rf_htf(rfLen, rfMultMin, rfMultMax, rfDynWin, win), win, rf_1)
f_process(tf2, rf_htf(rfLen, rfMultMin, rfMultMax, rfDynWin, win), win, rf_3)
f_process(tf3, rf_htf(rfLen, rfMultMin, rfMultMax, rfDynWin, win), win, rf_5)
f_process(tf4, rf_htf(rfLen, rfMultMin, rfMultMax, rfDynWin, win), win, rf_15)

f_process(tf1, adx_htf(adxLenDI, adxLen), win, adx_1)
f_process(tf2, adx_htf(adxLenDI, adxLen), win, adx_3)
f_process(tf3, adx_htf(adxLenDI, adxLen), win, adx_5)
f_process(tf4, adx_htf(adxLenDI, adxLen), win, adx_15)

f_process(tf1, norm_vol(volume, tf1), win, vol_1)
f_process(tf2, norm_vol(volume, tf2), win, vol_3)
f_process(tf3, norm_vol(volume, tf3), win, vol_5)
f_process(tf4, norm_vol(volume, tf4), win, vol_15)

f_process(tf1, tanh_norm(close, win), win, osc_1)
f_process(tf2, tanh_norm(close, win), win, osc_3)
f_process(tf3, tanh_norm(close, win), win, osc_5)
f_process(tf4, tanh_norm(close, win), win, osc_15)

comp_s_rf_val = composite(
   rolling_z(rf_1.lastSlope,  rf_1.slopes,  win),
   rolling_z(rf_3.lastSlope,  rf_3.slopes,  win),
   rolling_z(rf_5.lastSlope,  rf_5.slopes,  win),
   rolling_z(rf_15.lastSlope, rf_15.slopes, win),
   w1, w3, w5, w15
 )

comp_a_rf_val = composite(
   rolling_z(rf_1.lastAccel,  rf_1.accels,  win),
   rolling_z(rf_3.lastAccel,  rf_3.accels,  win),
   rolling_z(rf_5.lastAccel,  rf_5.accels,  win),
   rolling_z(rf_15.lastAccel, rf_15.accels, win),
   w1, w3, w5, w15
 )

comp_s_adx_val = composite(
   rolling_z(adx_1.lastSlope,  adx_1.slopes,  win),
   rolling_z(adx_3.lastSlope,  adx_3.slopes,  win),
   rolling_z(adx_5.lastSlope,  adx_5.slopes,  win),
   rolling_z(adx_15.lastSlope, adx_15.slopes, win),
   w1, w3, w5, w15
 )

comp_a_adx_val = composite(
   rolling_z(adx_1.lastAccel,  adx_1.accels,  win),
   rolling_z(adx_3.lastAccel,  adx_3.accels,  win),
   rolling_z(adx_5.lastAccel,  adx_5.accels,  win),
   rolling_z(adx_15.lastAccel, adx_15.accels, win),
   w1, w3, w5, w15
 )

array.push(comp_s_rf,   comp_s_rf_val)
array.push(comp_a_rf,   comp_a_rf_val)
array.push(comp_s_adx,  comp_s_adx_val)
array.push(comp_a_adx,  comp_a_adx_val)

if array.size(comp_s_rf) > gauss_len
    array.shift(comp_s_rf)
if array.size(comp_a_rf) > gauss_len
    array.shift(comp_a_rf)
if array.size(comp_s_adx) > gauss_len
    array.shift(comp_s_adx)
if array.size(comp_a_adx) > gauss_len
    array.shift(comp_a_adx)

gauss_s_rf   = gaussian_filter(comp_s_rf,   gauss_len, gauss_sig)
gauss_a_rf   = gaussian_filter(comp_a_rf,   gauss_len, gauss_sig)
gauss_s_adx  = gaussian_filter(comp_s_adx,  gauss_len, gauss_sig)
gauss_a_adx  = gaussian_filter(comp_a_adx,  gauss_len, gauss_sig)

// === ADVANCED AUTOMATED TRADING SIGNALS ===

// Multi-dimensional signal analysis
rf_slope_signal = nz(gauss_s_rf, 0)
rf_accel_signal = nz(gauss_a_rf, 0)
adx_slope_signal = nz(gauss_s_adx, 0)
adx_accel_signal = nz(gauss_a_adx, 0)

// Calculate momentum convergence across timeframes
momentum_convergence = (math.abs(rf_slope_signal) + math.abs(rf_accel_signal) +
                       math.abs(adx_slope_signal) + math.abs(adx_accel_signal)) / 4

// Direction consensus scoring
direction_score = 0.0
direction_score := direction_score + (rf_slope_signal > 0 ? 1 : rf_slope_signal < 0 ? -1 : 0) * 0.3
direction_score := direction_score + (rf_accel_signal > 0 ? 1 : rf_accel_signal < 0 ? -1 : 0) * 0.25
direction_score := direction_score + (adx_slope_signal > 0 ? 1 : adx_slope_signal < 0 ? -1 : 0) * 0.25
direction_score := direction_score + (adx_accel_signal > 0 ? 1 : adx_accel_signal < 0 ? -1 : 0) * 0.2

// Signal quality assessment
signal_quality = momentum_convergence * math.abs(direction_score)

// Regime detection for adaptive thresholds
market_regime = momentum_convergence > 1.5 ? "trending" : momentum_convergence > 0.8 ? "transitional" : "ranging"
adaptive_entry_threshold = market_regime == "trending" ? 0.8 : market_regime == "transitional" ? 1.2 : 1.8
adaptive_exit_threshold = market_regime == "trending" ? 0.4 : market_regime == "transitional" ? 0.6 : 0.9

// Entry conditions with regime adaptation
entry_long_condition = direction_score > adaptive_entry_threshold and signal_quality > 0.6 and rf_slope_signal > 0.5
entry_short_condition = direction_score < -adaptive_entry_threshold and signal_quality > 0.6 and rf_slope_signal < -0.5

// Exit conditions with momentum decay detection
exit_long_condition = direction_score < adaptive_exit_threshold or signal_quality < 0.3 or rf_accel_signal < -0.8
exit_short_condition = direction_score > -adaptive_exit_threshold or signal_quality < 0.3 or rf_accel_signal > 0.8

// Position tracking with sophisticated state management
var string position_state = "flat"
var float entry_price = na
var int bars_in_position = 0
var float max_favorable_excursion = 0.0
var float max_adverse_excursion = 0.0

// Position management logic
if position_state == "flat"
    if entry_long_condition
        position_state := "long"
        entry_price := close
        bars_in_position := 0
        max_favorable_excursion := 0.0
        max_adverse_excursion := 0.0
    else if entry_short_condition
        position_state := "short"
        entry_price := close
        bars_in_position := 0
        max_favorable_excursion := 0.0
        max_adverse_excursion := 0.0

if position_state == "long"
    bars_in_position := bars_in_position + 1
    current_pnl = (close - entry_price) / entry_price * 100
    max_favorable_excursion := math.max(max_favorable_excursion, current_pnl)
    max_adverse_excursion := math.min(max_adverse_excursion, current_pnl)

    if exit_long_condition or bars_in_position > 20 or current_pnl < -2.5 or current_pnl > 4.0
        position_state := "flat"

if position_state == "short"
    bars_in_position := bars_in_position + 1
    current_pnl = (entry_price - close) / entry_price * 100
    max_favorable_excursion := math.max(max_favorable_excursion, current_pnl)
    max_adverse_excursion := math.min(max_adverse_excursion, current_pnl)

    if exit_short_condition or bars_in_position > 20 or current_pnl < -2.5 or current_pnl > 4.0
        position_state := "flat"

// Generate sophisticated alerts
if enable_alerts
    if position_state == "long" and position_state[1] == "flat"
        alert_message = webhook_enabled ?
         '{"action":"buy","symbol":"ETHUSDT","price":' + str.tostring(close, "#.##") + ',"quality":' + str.tostring(signal_quality, "#.##") + ',"regime":"' + market_regime + '"}' :
         "LONG: ETH/USDT @ " + str.tostring(close, "#.##") + " | Quality: " + str.tostring(signal_quality, "#.##") + " | Regime: " + market_regime + " | Dir: " + str.tostring(direction_score, "#.##")
        alert(alert_message, alert.freq_once_per_bar)

    if position_state == "short" and position_state[1] == "flat"
        alert_message = webhook_enabled ?
         '{"action":"sell","symbol":"ETHUSDT","price":' + str.tostring(close, "#.##") + ',"quality":' + str.tostring(signal_quality, "#.##") + ',"regime":"' + market_regime + '"}' :
         "SHORT: ETH/USDT @ " + str.tostring(close, "#.##") + " | Quality: " + str.tostring(signal_quality, "#.##") + " | Regime: " + market_regime + " | Dir: " + str.tostring(direction_score, "#.##")
        alert(alert_message, alert.freq_once_per_bar)

    if position_state == "flat" and position_state[1] != "flat"
        pnl = position_state[1] == "long" ? (close - entry_price) / entry_price * 100 : (entry_price - close) / entry_price * 100
        alert_message = webhook_enabled ?
         '{"action":"close","symbol":"ETHUSDT","pnl":' + str.tostring(pnl, "#.##") + '}' :
         "CLOSE: " + position_state[1] + " | PnL: " + str.tostring(pnl, "#.##") + "% | MFE: " + str.tostring(max_favorable_excursion, "#.##") + "% | MAE: " + str.tostring(max_adverse_excursion, "#.##") + "%"
        alert(alert_message, alert.freq_once_per_bar)

plot(comp_s_rf_val,   color=color.green,    title="Comp RF Slope",      linewidth=2)
plot(comp_a_rf_val,   color=color.teal,     title="Comp RF Accel",      linewidth=2)
plot(comp_s_adx_val,  color=color.orange,   title="Comp ADX Slope",     linewidth=2)
plot(comp_a_adx_val,  color=color.maroon,   title="Comp ADX Accel",     linewidth=2)
plot(gauss_s_rf,      color=color.lime,     title="Gauss Comp RF Slope",    linewidth=3)
plot(gauss_a_rf,      color=color.blue,     title="Gauss Comp RF Accel",    linewidth=3)
plot(gauss_s_adx,     color=color.fuchsia,  title="Gauss Comp ADX Slope",   linewidth=3)
plot(gauss_a_adx,     color=color.red,      title="Gauss Comp ADX Accel",   linewidth=3)

// === ADVANCED SIGNAL VISUALIZATION ===

// Plot sophisticated trading metrics
plot(direction_score, "Direction Score", color=color.new(color.white, 0), linewidth=3)
plot(signal_quality, "Signal Quality", color=color.new(color.yellow, 0), linewidth=2)
plot(momentum_convergence, "Momentum Convergence", color=color.new(color.purple, 0), linewidth=2)

// Plot adaptive thresholds
plot(adaptive_entry_threshold, "Entry Threshold", color=color.new(color.lime, 50), linewidth=1, style=plot.style_stepline)
plot(-adaptive_entry_threshold, "Entry Threshold", color=color.new(color.red, 50), linewidth=1, style=plot.style_stepline)

// Plot position signals
plot(position_state == "long" and position_state[1] == "flat" ? 4 : na, "Long Entry", color=color.new(color.lime, 0), style=plot.style_circles, linewidth=6)
plot(position_state == "short" and position_state[1] == "flat" ? -4 : na, "Short Entry", color=color.new(color.red, 0), style=plot.style_circles, linewidth=6)
plot(position_state == "flat" and position_state[1] == "long" ? 3.5 : na, "Exit Long", color=color.new(color.orange, 0), style=plot.style_cross, linewidth=4)
plot(position_state == "flat" and position_state[1] == "short" ? -3.5 : na, "Exit Short", color=color.new(color.orange, 0), style=plot.style_cross, linewidth=4)

// Market regime visualization
regime_color = market_regime == "trending" ? color.new(color.green, 80) :
               market_regime == "transitional" ? color.new(color.yellow, 80) :
               color.new(color.red, 80)
bgcolor(regime_color, title="Market Regime")

// Position status background
position_bg = position_state == "long" ? color.new(color.lime, 95) :
              position_state == "short" ? color.new(color.red, 95) : na
bgcolor(position_bg, title="Position Status")

hline( 3, "Z+3", color=color.gray)
hline( 2, "Z+2", color=color.gray)
hline( 1, "Z+1", color=color.silver)
hline( 0, "Z0",  color=color.white)
hline(-1, "Z-1", color=color.silver)
hline(-2, "Z-2", color=color.gray)
hline(-3, "Z-3", color=color.gray)