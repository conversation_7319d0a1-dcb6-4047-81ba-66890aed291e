// © cryptokairo — MPL-2.0
//@version=5
indicator("S&K ADX/RF MTF True Dynamic Z-Score Slope/Accel + Composite & Gaussian + Signals", overlay=false, max_labels_count=500)

// === INDICATOR SETTINGS ===
rfLen       = input.int(14, "RF Length", group="Indicators")
rfMultMin   = input.float(3.0, "RF Min Mult", group="Indicators")
rfMultMax   = input.float(6.0, "RF Max Mult", group="Indicators")
rfDynWin    = input.int(54, "RF Dynamic Multi Z Win", group="Indicators")
adxLenDI    = input.int(54, "ADX DI Length", group="Indicators")
adxLen      = input.int(17, "ADX Smoothing", group="Indicators")
win         = input.int(90, "Rolling Z Window", minval=20, maxval=500, group="Indicators")
gauss_len   = input.int(7, "Gaussian Filter Length", group="Indicators")
gauss_sig   = input.float(2, "Gaussian Filter Sigma", group="Indicators")

// === TIMEFRAME SETTINGS ===
tf1         = input.timeframe("15","TF-1", group="Timeframes")
tf2         = input.timeframe("30","TF-2", group="Timeframes")
tf3         = input.timeframe("45","TF-3", group="Timeframes")
tf4         = input.timeframe("60","TF-4", group="Timeframes")

// === SIGNAL GENERATION SETTINGS ===
signal_group = "Signal Generation"
enable_signals    = input.bool(true, "Enable Signal Generation", group=signal_group)
entry_threshold   = input.float(1.5, "Entry Signal Threshold", minval=0.1, maxval=5.0, step=0.1, group=signal_group)
exit_threshold    = input.float(0.5, "Exit Signal Threshold", minval=0.1, maxval=3.0, step=0.1, group=signal_group)
min_agreement     = input.int(2, "Min Indicator Agreement", minval=1, maxval=4, group=signal_group)
use_gaussian      = input.bool(true, "Use Gaussian Filtered Signals", group=signal_group)
signal_sensitivity = input.string("Medium", "Signal Sensitivity", options=["Low", "Medium", "High"], group=signal_group)

// === ALERT SETTINGS ===
alert_group = "Alerts"
enable_alerts     = input.bool(false, "Enable Alerts", group=alert_group)
alert_on_entry    = input.bool(true, "Alert on Entry Signals", group=alert_group)
alert_on_exit     = input.bool(true, "Alert on Exit Signals", group=alert_group)
alert_on_strength = input.bool(false, "Alert on Strength Changes", group=alert_group)

zscore(src, len) =>
    mu  = ta.sma(src, len)
    sig = ta.stdev(src, len)
    sig == 0 ? na : (src - mu) / sig

dyn_mult(z, minMult, maxMult) =>
    scale = math.min(math.abs(z) / 2.5, 1)
    minMult + (maxMult - minMult) * scale

rf_htf(len, minMult, maxMult, dynWin, stdWin) =>
    cmo   = ta.cmo(close, len)
    cmoZ  = zscore(cmo, dynWin)
    mult  = dyn_mult(cmoZ, minMult, maxMult)
    width = ta.stdev(close - close[1], stdWin) * mult
    var float rf_state = na
    rf_state := na(rf_state) ? close : rf_state
    rf_state := close > rf_state ? math.max(rf_state, close - width) : close < rf_state ? math.min(rf_state, close + width) : rf_state
    rf_state

adx_htf(lenDI, lenADX) =>
    [_, _, adxVal] = ta.dmi(lenDI, lenADX)
    adxVal

rolling_z(val, arr, win) =>
    sz = array.size(arr)
    if sz < win or na(val)
        na
    else
        mu = 0.0
        for i = sz - win to sz - 1
            mu += array.get(arr, i)
        mu /= win
        sig = 0.0
        for i = sz - win to sz - 1
            sig += math.pow(array.get(arr, i) - mu, 2)
        sig := math.sqrt(sig / win)
        sig == 0 ? na : (val - mu) / sig

gaussian_filter(arr, length, sigma) =>
    sz = array.size(arr)
    if sz < length
        na
    else
        sum   = 0.0
        total = 0.0
        pi = math.pi
        for i = 0 to length - 1
            weight = math.exp(-0.5 * math.pow((i - length/2) / sigma, 2.0)) / math.sqrt(sigma * 2.0 * pi)
            val = array.get(arr, sz - 1 - i)
            if not na(val)
                sum   += val * weight
                total += weight
        total == 0 ? na : sum / total

get_last(arr) =>
    array.size(arr) > 0 ? array.get(arr, array.size(arr) - 1) : na

norm_vol(vol, tf) =>
    minv = request.security(syminfo.tickerid, tf, ta.lowest(volume, win))
    maxv = request.security(syminfo.tickerid, tf, ta.highest(volume, win))
    (vol - minv) / (maxv - minv + 1e-9)
    
composite(a, b, c, d, w1, w2, w3, w4) =>
    num = nz(a) * w1 + nz(b) * w2 + nz(c) * w3 + nz(d) * w4
    den = nz(w1) + nz(w2) + nz(w3) + nz(w4)
    den != 0 ? num / den : na

tanh(series float _src) =>
    -1 + 2/(1 + math.exp(-2*_src))

tanh_norm(src, len) =>
    diff = src - src[1]
    rms = ta.stdev(diff, len) + 1e-9
    tanh(diff / rms)

normalizeDeriv(_src, _quadraticMeanLength) =>
    derivative = _src - _src[2]
    qMean = math.sqrt(nz(math.sum(math.pow(derivative, 2), _quadraticMeanLength)) / _quadraticMeanLength)
    qMean != 0 ? derivative / qMean : na

// === SIGNAL GENERATION FUNCTIONS ===
get_sensitivity_multiplier() =>
    switch signal_sensitivity
        "Low"    => 0.7
        "Medium" => 1.0
        "High"   => 1.3
        => 1.0

calculate_signal_strength(rf_slope, rf_accel, adx_slope, adx_accel) =>
    // Calculate individual component strengths
    rf_strength = math.abs(nz(rf_slope, 0)) + math.abs(nz(rf_accel, 0))
    adx_strength = math.abs(nz(adx_slope, 0)) + math.abs(nz(adx_accel, 0))

    // Combined strength with weighting
    total_strength = (rf_strength * 0.6) + (adx_strength * 0.4)
    total_strength

determine_signal_direction(rf_slope, rf_accel, adx_slope, adx_accel) =>
    // Count positive and negative signals
    positive_signals = 0
    negative_signals = 0

    if nz(rf_slope, 0) > 0
        positive_signals += 1
    else if nz(rf_slope, 0) < 0
        negative_signals += 1

    if nz(rf_accel, 0) > 0
        positive_signals += 1
    else if nz(rf_accel, 0) < 0
        negative_signals += 1

    if nz(adx_slope, 0) > 0
        positive_signals += 1
    else if nz(adx_slope, 0) < 0
        negative_signals += 1

    if nz(adx_accel, 0) > 0
        positive_signals += 1
    else if nz(adx_accel, 0) < 0
        negative_signals += 1

    // Determine direction based on agreement
    net_direction = positive_signals - negative_signals
    agreement_count = positive_signals + negative_signals

    direction = net_direction > 0 ? 1 : net_direction < 0 ? -1 : 0
    has_agreement = agreement_count >= min_agreement

    [direction, has_agreement, positive_signals, negative_signals]

generate_entry_signal(strength, direction, has_agreement, use_gauss_values) =>
    if not enable_signals
        [false, false, 0]
    else
        sensitivity_mult = get_sensitivity_multiplier()
        adjusted_threshold = entry_threshold * sensitivity_mult

        is_strong_enough = strength >= adjusted_threshold
        entry_long = direction > 0 and has_agreement and is_strong_enough
        entry_short = direction < 0 and has_agreement and is_strong_enough

        signal_strength_level = strength >= (adjusted_threshold * 2) ? 3 :
                               strength >= (adjusted_threshold * 1.5) ? 2 :
                               strength >= adjusted_threshold ? 1 : 0

        [entry_long, entry_short, signal_strength_level]

generate_exit_signal(strength, prev_direction, current_direction) =>
    if not enable_signals
        [false, false]
    else
        sensitivity_mult = get_sensitivity_multiplier()
        adjusted_threshold = exit_threshold * sensitivity_mult

        // Exit on weakness or direction change
        exit_long = (prev_direction > 0 and (strength < adjusted_threshold or current_direction <= 0))
        exit_short = (prev_direction < 0 and (strength < adjusted_threshold or current_direction >= 0))

        [exit_long, exit_short]

type Metrics
    float[] vals
    float[] slopes
    float[] accels
    float lastVal
    float lastSlope
    float lastAccel

newMetrics() =>
   Metrics.new(
       array.new<float>(), 
       array.new<float>(), 
       array.new<float>(), 
       na, na, na
     )

f_pushShift(arr, val, len) =>
    array.push(arr, val)
    if array.size(arr) > len
        array.shift(arr)

f_process(tf, func, len, met) =>
    val = request.security(syminfo.tickerid, tf, func)
    f_pushShift(met.vals, val, len)
    sz = array.size(met.vals)
    sz_slope = array.size(met.slopes)

    // Calculate slope with bounds checking
    slope = na
    if sz >= 2
        current_val = array.get(met.vals, sz - 1)
        slope = normalizeDeriv(current_val, len)

    // Calculate acceleration with bounds checking
    accel = na
    if sz_slope >= 2
        current_slope = array.get(met.slopes, sz_slope - 1)
        accel = normalizeDeriv(current_slope, len)

    // Only push slope if we have a valid value
    if not na(slope)
        f_pushShift(met.slopes, slope, len)

    // Only push acceleration if we have a valid value
    if not na(accel)
        f_pushShift(met.accels, accel, len)

    met.lastVal   := val
    met.lastSlope := slope
    met.lastAccel := accel

var Metrics rf_1   = newMetrics()
var Metrics rf_3   = newMetrics()
var Metrics rf_5   = newMetrics()
var Metrics rf_15  = newMetrics()

var Metrics adx_1  = newMetrics()
var Metrics adx_3  = newMetrics()
var Metrics adx_5  = newMetrics()
var Metrics adx_15 = newMetrics()

var Metrics vol_1  = newMetrics()
var Metrics vol_3  = newMetrics()
var Metrics vol_5  = newMetrics()
var Metrics vol_15 = newMetrics()

var Metrics osc_1  = newMetrics()
var Metrics osc_3  = newMetrics()
var Metrics osc_5  = newMetrics()
var Metrics osc_15 = newMetrics()

var float[] comp_s_rf   = array.new_float()
var float[] comp_a_rf   = array.new_float()
var float[] comp_s_adx  = array.new_float()
var float[] comp_a_adx  = array.new_float()

tfs = array.from(tf1, tf2, tf3, tf4)
rf_metrics  = array.from(rf_1, rf_3, rf_5, rf_15)
adx_metrics = array.from(adx_1, adx_3, adx_5, adx_15)
vol_metrics = array.from(vol_1, vol_3, vol_5, vol_15)
osc_metrics = array.from(osc_1, osc_3, osc_5, osc_15)

w1  = vol_1.lastVal
w3  = vol_3.lastVal
w5  = vol_5.lastVal
w15 = vol_15.lastVal

f_process(tf1, rf_htf(rfLen, rfMultMin, rfMultMax, rfDynWin, win), win, rf_1)
f_process(tf2, rf_htf(rfLen, rfMultMin, rfMultMax, rfDynWin, win), win, rf_3)
f_process(tf3, rf_htf(rfLen, rfMultMin, rfMultMax, rfDynWin, win), win, rf_5)
f_process(tf4, rf_htf(rfLen, rfMultMin, rfMultMax, rfDynWin, win), win, rf_15)

f_process(tf1, adx_htf(adxLenDI, adxLen), win, adx_1)
f_process(tf2, adx_htf(adxLenDI, adxLen), win, adx_3)
f_process(tf3, adx_htf(adxLenDI, adxLen), win, adx_5)
f_process(tf4, adx_htf(adxLenDI, adxLen), win, adx_15)

f_process(tf1, norm_vol(volume, tf1), win, vol_1)
f_process(tf2, norm_vol(volume, tf2), win, vol_3)
f_process(tf3, norm_vol(volume, tf3), win, vol_5)
f_process(tf4, norm_vol(volume, tf4), win, vol_15)

f_process(tf1, tanh_norm(close, win), win, osc_1)
f_process(tf2, tanh_norm(close, win), win, osc_3)
f_process(tf3, tanh_norm(close, win), win, osc_5)
f_process(tf4, tanh_norm(close, win), win, osc_15)

comp_s_rf_val = composite(
   rolling_z(rf_1.lastSlope,  rf_1.slopes,  win),
   rolling_z(rf_3.lastSlope,  rf_3.slopes,  win),
   rolling_z(rf_5.lastSlope,  rf_5.slopes,  win),
   rolling_z(rf_15.lastSlope, rf_15.slopes, win),
   w1, w3, w5, w15
 )

comp_a_rf_val = composite(
   rolling_z(rf_1.lastAccel,  rf_1.accels,  win),
   rolling_z(rf_3.lastAccel,  rf_3.accels,  win),
   rolling_z(rf_5.lastAccel,  rf_5.accels,  win),
   rolling_z(rf_15.lastAccel, rf_15.accels, win),
   w1, w3, w5, w15
 )

comp_s_adx_val = composite(
   rolling_z(adx_1.lastSlope,  adx_1.slopes,  win),
   rolling_z(adx_3.lastSlope,  adx_3.slopes,  win),
   rolling_z(adx_5.lastSlope,  adx_5.slopes,  win),
   rolling_z(adx_15.lastSlope, adx_15.slopes, win),
   w1, w3, w5, w15
 )

comp_a_adx_val = composite(
   rolling_z(adx_1.lastAccel,  adx_1.accels,  win),
   rolling_z(adx_3.lastAccel,  adx_3.accels,  win),
   rolling_z(adx_5.lastAccel,  adx_5.accels,  win),
   rolling_z(adx_15.lastAccel, adx_15.accels, win),
   w1, w3, w5, w15
 )

array.push(comp_s_rf,   comp_s_rf_val)
array.push(comp_a_rf,   comp_a_rf_val)
array.push(comp_s_adx,  comp_s_adx_val)
array.push(comp_a_adx,  comp_a_adx_val)

if array.size(comp_s_rf) > gauss_len
    array.shift(comp_s_rf)
if array.size(comp_a_rf) > gauss_len
    array.shift(comp_a_rf)
if array.size(comp_s_adx) > gauss_len
    array.shift(comp_s_adx)
if array.size(comp_a_adx) > gauss_len
    array.shift(comp_a_adx)

gauss_s_rf   = gaussian_filter(comp_s_rf,   gauss_len, gauss_sig)
gauss_a_rf   = gaussian_filter(comp_a_rf,   gauss_len, gauss_sig)
gauss_s_adx  = gaussian_filter(comp_s_adx,  gauss_len, gauss_sig)
gauss_a_adx  = gaussian_filter(comp_a_adx,  gauss_len, gauss_sig)

// === SIGNAL GENERATION ===
// Use Gaussian filtered values if enabled, otherwise use raw composite values
signal_rf_slope = use_gaussian ? gauss_s_rf : comp_s_rf_val
signal_rf_accel = use_gaussian ? gauss_a_rf : comp_a_rf_val
signal_adx_slope = use_gaussian ? gauss_s_adx : comp_s_adx_val
signal_adx_accel = use_gaussian ? gauss_a_adx : comp_a_adx_val

// Calculate signal strength and direction
signal_strength = calculate_signal_strength(signal_rf_slope, signal_rf_accel, signal_adx_slope, signal_adx_accel)
[signal_direction, has_agreement, pos_signals, neg_signals] = determine_signal_direction(signal_rf_slope, signal_rf_accel, signal_adx_slope, signal_adx_accel)

// Generate entry and exit signals
[entry_long, entry_short, strength_level] = generate_entry_signal(signal_strength, signal_direction, has_agreement, use_gaussian)

// Track previous direction for exit signals
var int prev_signal_direction = 0
[exit_long, exit_short] = generate_exit_signal(signal_strength, prev_signal_direction, signal_direction)
prev_signal_direction := signal_direction

// Create alert conditions
entry_alert = (entry_long or entry_short) and enable_alerts and alert_on_entry
exit_alert = (exit_long or exit_short) and enable_alerts and alert_on_exit
strength_alert = (strength_level >= 2) and enable_alerts and alert_on_strength

// Generate alerts
if entry_alert
    direction_text = entry_long ? "LONG" : "SHORT"
    alert("Entry Signal: " + direction_text + " | Strength: " + str.tostring(strength_level) + " | Agreement: " + str.tostring(pos_signals) + "/" + str.tostring(neg_signals), alert.freq_once_per_bar)

if exit_alert
    direction_text = exit_long ? "EXIT LONG" : "EXIT SHORT"
    alert("Exit Signal: " + direction_text + " | Strength: " + str.tostring(signal_strength, "#.##"), alert.freq_once_per_bar)

if strength_alert
    alert("High Strength Signal: " + str.tostring(signal_strength, "#.##") + " | Direction: " + str.tostring(signal_direction), alert.freq_once_per_bar)

plot(comp_s_rf_val,   color=color.green,    title="Comp RF Slope",      linewidth=2)
plot(comp_a_rf_val,   color=color.teal,     title="Comp RF Accel",      linewidth=2)
plot(comp_s_adx_val,  color=color.orange,   title="Comp ADX Slope",     linewidth=2)
plot(comp_a_adx_val,  color=color.maroon,   title="Comp ADX Accel",     linewidth=2)
plot(gauss_s_rf,      color=color.lime,     title="Gauss Comp RF Slope",    linewidth=3)
plot(gauss_a_rf,      color=color.blue,     title="Gauss Comp RF Accel",    linewidth=3)
plot(gauss_s_adx,     color=color.fuchsia,  title="Gauss Comp ADX Slope",   linewidth=3)
plot(gauss_a_adx,     color=color.red,      title="Gauss Comp ADX Accel",   linewidth=3)

// === SIGNAL VISUALIZATION ===
// Plot signal strength
plot(enable_signals ? signal_strength : na, color=color.new(color.white, 0), title="Signal Strength", linewidth=2)

// Plot entry signals
plot(entry_long ? 4 : na, color=color.new(color.lime, 0), title="Entry Long", style=plot.style_circles, linewidth=4)
plot(entry_short ? -4 : na, color=color.new(color.red, 0), title="Entry Short", style=plot.style_circles, linewidth=4)

// Plot exit signals
plot(exit_long ? 3.5 : na, color=color.new(color.orange, 0), title="Exit Long", style=plot.style_cross, linewidth=3)
plot(exit_short ? -3.5 : na, color=color.new(color.orange, 0), title="Exit Short", style=plot.style_cross, linewidth=3)

// Plot signal strength levels
plot(strength_level >= 3 ? 4.5 : na, color=color.new(color.yellow, 0), title="High Strength", style=plot.style_diamond, linewidth=2)
plot(strength_level == 2 ? 4.2 : na, color=color.new(color.aqua, 0), title="Medium Strength", style=plot.style_diamond, linewidth=2)

hline( 3, "Z+3", color=color.gray)
hline( 2, "Z+2", color=color.gray)
hline( 1, "Z+1", color=color.silver)
hline( 0, "Z0",  color=color.white)
hline(-1, "Z-1", color=color.silver)
hline(-2, "Z-2", color=color.gray)
hline(-3, "Z-3", color=color.gray)