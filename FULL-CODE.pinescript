//@version=5
strategy("S&K MTF CMO Screener", overlay=false,
  commission_type=strategy.commission.percent, commission_value=0.00,
  default_qty_type=strategy.percent_of_equity, default_qty_value=100,
  initial_capital=2000, slippage=0, calc_on_every_tick=true)

// Colors
COLOR_GRAY = color.rgb(87,89,94)
COLOR_BLUE = color.rgb(64,118,179)
COLOR_TREND_BULL = color.rgb(16,228,217)
COLOR_TREND_BEAR = color.rgb(201,206,63)
COLOR_SUPPLY = color.rgb(170,0,10)
COLOR_DEMAND = color.rgb(0,190,120)
COLOR_SLOPE_UP = color.rgb(147,212,68)
COLOR_SLOPE_DOWN = color.rgb(206,60,140)
COLOR_SLOPE_NEUTRAL = color.rgb(165,165,165)

// Constants and globals
var int NoTrend = 0
var int WeakTrend = 1
var int StrongTrend = 2
s(v) => int(v) // Convert to simple int

// Arrays for state tracking
var float[] rfLines = array.new_float(4, na)
var float[] mults = array.new_float(4, na)
var float[] adxVals = array.new_float(4, na)
var float[] plusDIs = array.new_float(4, na)
var float[] minusDIs = array.new_float(4, na)
var float[] adxSlopes = array.new_float(4, na)
var float[] adxZscores = array.new_float(4, na)
var float[] weights = array.new_float(4, na)
var int[] rfUps = array.new_int(4, 0)
var int[] rfDns = array.new_int(4, 0)
var int[] rfStates = array.new_int(4, 0)
var int[] states = array.new_int(4, 0)
var int[] directions = array.new_int(4, 0)
var bool[] rfValids = array.new_bool(4, false)
var bool[] validFlags = array.new_bool(4, false)

// Composite tracking
var float compositeScore = 0.0
var int directionSum = 0
var int validTrendCount = 0
var bool entrySignal = false
// Types
type cfgRange
    float src
    float defaultMult
    float bullMult
    float bullCMO
    float neutMult
    float neutCMO
    float bearMult
    float bearCMO
    int period
    int smoothing
    int cmoPeriod
    int look

type cfgAdx
    bool useFilter
    bool useDynamicLimits
    int length
    int threshold
    int upperLimit
    int lowerLimit
    int cmoPeriod
    color dynamicColor
    color defaultColor

type cfgDisplay
    bool isShowTrend
    bool isShowLimits
    bool isShowSlope
    float slopeThreshold
    float slopeDisplacement
    color colorSlopeUp
    color colorSlopeDown
    color colorSlopeNeutral
    int slopeLineWidth

type cfgScreener
    bool useTF1
    bool useTF2
    bool useTF3
    bool useTF4
    bool minDir_tf
    bool minAcc_tf
    bool minSlp_tf
    bool rfInvert_tf1
    bool rfInvert_tf2
    bool rfInvert_tf3
    bool rfInvert_tf4
    string tf1
    string tf2
    string tf3
    string tf4
    float weightTF1
    float weightTF2
    float weightTF3
    float weightTF4
    float minAc_tf1
    float minSl_tf1
    float minAc_tf2
    float minSl_tf2
    float minAc_tf3
    float minSl_tf3
    float minAc_tf4
    float minSl_tf4
    float minCompositeScore
    int minUp_tf1
    int minDn_tf1
    int rfMode_tf1
    int minUp_tf2
    int minDn_tf2
    int rfMode_tf2
    int minUp_tf3
    int minDn_tf3
    int rfMode_tf3
    int minUp_tf4
    int minDn_tf4
    int rfMode_tf4
    int minValidCount
    int minDirectionAgreement

type MtfScreenerResult
    float rangeFilter
    float adxValue
    float adxPlusDI
    float adxMinusDI
    float adxSlope
    float adxZscore
    float mult
    int rangeUpCount
    int rangeDownCount

// Type definitions for timeframe settings using the "Features" pattern
type TimeframeSettings
    bool enabled
    string timeframe
    float weight
    int minUp
    int minDn
    float minAc
    float minSl
    int rfMode
    bool rfInvert

type TimeframeArrays
    array<bool> enabled
    array<string> timeframes
    array<float> weights
    array<int> minUps
    array<int> minDns
    array<float> minAcs
    array<float> minSls
    array<int> rfModes
    array<bool> rfInverts
// Range Filter inputs
rf_group = 'Range Filter'
rf_src = input(close, 'Source', group=rf_group)
rf_period = input.int(34, 'Range Period', group=rf_group)
rf_smoothing = input.int(78, 'Smoothing Len', group=rf_group)
rf_cmoPeriod = input.int(54, 'CMO Z Period', group=rf_group)
rf_multDefault = input.float(6.0, 'Default Multi', group=rf_group)
rf_multBull = input.float(0.69, 'Bull Multi', group=rf_group, inline='bull')
rf_cmoBull = input.float(1.0, 'Bull CMO Z', group=rf_group, inline='bull')
rf_multNeut = input.float(3.0, 'Neutral Multi', group=rf_group, inline='neut')
rf_cmoNeut = input.float(0.5, 'Neutral CMO Z', group=rf_group, inline='neut')
rf_multBear = input.float(5.0, 'Bear Multi', group=rf_group, inline='bear')
rf_cmoBear = input.float(0.0, 'Bear CMO Z', group=rf_group, inline='bear')
rf_lookback = input.int(84, 'LookBack', group=rf_group)

// ADX inputs
adx_group = 'ADX'
adx_useFilter = input.bool(true, 'Use ADX Filter?', group=adx_group)
adx_len = input.int(12, minval=1, 'Filter Length', group=adx_group, inline='1')
adx_threshold = input.int(20, minval=-100, 'Filter Threshold', group=adx_group, inline='1')
adx_upperLimit = input.int(60, minval=50, 'Upper Limit', group=adx_group, inline='2')
adx_lowerLimit = input.int(15, minval=1, 'Lower Limit', group=adx_group, inline='2')
adx_dynLimits = input.bool(true, 'Use Dyn Limits?', group=adx_group)
adx_cmoPeriod = input.int(84, minval=2, 'CMO Period', group=adx_group)
adx_colorLimits = input.color(COLOR_GRAY, 'Limits Color', group=adx_group)
adx_colorDefault = input.color(COLOR_BLUE, 'Default Color', group=adx_group)

// Display inputs
display_group = 'Display'
disp_trend_enable = input.bool(true, 'Enable Trend Display', group=display_group)
disp_trend_bullColor = input.color(COLOR_TREND_BULL, 'Bull Trend', group=display_group, inline='trendColors')
disp_trend_bearColor = input.color(COLOR_TREND_BEAR, 'Bear Trend', group=display_group, inline='trendColors')
disp_limit_enable = input.bool(true, 'Enable Limits Display', group=display_group)
disp_limit_supColor = input.color(COLOR_SUPPLY, 'Supply Zone', group=display_group, inline='limitColors')
disp_limit_demColor = input.color(COLOR_DEMAND, 'Demand Zone', group=display_group, inline='limitColors')
disp_slope_enable = input.bool(true, 'Enable Slope Display', group=display_group)
disp_slope_thresh = input.float(15, minval=0.01, 'Slope Threshold', group=display_group)
disp_slope_disp = input.float(5, minval=0.01, 'Slope Displacement', group=display_group)
disp_slope_upColor = input.color(COLOR_SLOPE_UP, 'Up Slope', group=display_group, inline='slopeColors')
disp_slope_dnColor = input.color(COLOR_SLOPE_DOWN, 'Down Slope', group=display_group, inline='slopeColors')
disp_slope_neuColor = input.color(COLOR_SLOPE_NEUTRAL, 'Neutral Slope', group=display_group, inline='slopeColors')
disp_slope_width = input.int(1, minval=1, 'Slope Dot Width', group=display_group)

// Screener inputs
screener_group = 'Screener'

// Define timeframe settings using the "Features" pattern
// This follows the pattern in EXAMPLE-PINE.pinescript where we define a single set of inputs
// and use arrays to store the settings for each timeframe

// Default values for timeframes
var string[] tf_names = array.new_string(4)
array.set(tf_names, 0, "TF1")
array.set(tf_names, 1, "TF2")
array.set(tf_names, 2, "TF3")
array.set(tf_names, 3, "TF4")

var string[] tf_periods = array.new_string(4)
array.set(tf_periods, 0, "15")
array.set(tf_periods, 1, "30")
array.set(tf_periods, 2, "45")
array.set(tf_periods, 3, "60")

var float[] tf_default_weights = array.new_float(4)
array.set(tf_default_weights, 0, 1.0)
array.set(tf_default_weights, 1, 0.75)
array.set(tf_default_weights, 2, 0.5)
array.set(tf_default_weights, 3, 0.25)

// Timeframe enable inputs
tf_enabled_1 = input.bool(true, "Enable TF1", group=screener_group, inline='usetf')
tf_enabled_2 = input.bool(true, "Enable TF2", group=screener_group, inline='usetf')
tf_enabled_3 = input.bool(true, "Enable TF3", group=screener_group, inline='usetf')
tf_enabled_4 = input.bool(true, "Enable TF4", group=screener_group, inline='usetf')

// Timeframe settings - using the "Features" pattern
// We define inputs for each timeframe, but use a consistent pattern
tf_timeframe_1 = input.timeframe(array.get(tf_periods, 0), array.get(tf_names, 0), group=screener_group, inline='tf1')
tf_weight_1 = input.float(array.get(tf_default_weights, 0), "Weight", group=screener_group, inline='tf1')
tf_minUp_1 = input.int(2, "MinUp", group=screener_group, inline='tf1')
tf_minDn_1 = input.int(2, "MinDn", group=screener_group, inline='tf1')
tf_minAc_1 = input.float(1.0, "MinZ", group=screener_group, inline='tf1')
tf_minSl_1 = input.float(10, "MinSl", group=screener_group, inline='tf1')
tf_rfMode_1 = input.int(1, "RF Mode", options=[-1,0,1], group=screener_group, inline='tf1')
tf_rfInv_1 = input.bool(false, "Invert", group=screener_group, inline='tf1')

tf_timeframe_2 = input.timeframe(array.get(tf_periods, 1), array.get(tf_names, 1), group=screener_group, inline='tf2')
tf_weight_2 = input.float(array.get(tf_default_weights, 1), "Weight", group=screener_group, inline='tf2')
tf_minUp_2 = input.int(2, "MinUp", group=screener_group, inline='tf2')
tf_minDn_2 = input.int(2, "MinDn", group=screener_group, inline='tf2')
tf_minAc_2 = input.float(1.0, "MinZ", group=screener_group, inline='tf2')
tf_minSl_2 = input.float(10, "MinSl", group=screener_group, inline='tf2')
tf_rfMode_2 = input.int(1, "RF Mode", options=[-1,0,1], group=screener_group, inline='tf2')
tf_rfInv_2 = input.bool(false, "Invert", group=screener_group, inline='tf2')

tf_timeframe_3 = input.timeframe(array.get(tf_periods, 2), array.get(tf_names, 2), group=screener_group, inline='tf3')
tf_weight_3 = input.float(array.get(tf_default_weights, 2), "Weight", group=screener_group, inline='tf3')
tf_minUp_3 = input.int(2, "MinUp", group=screener_group, inline='tf3')
tf_minDn_3 = input.int(2, "MinDn", group=screener_group, inline='tf3')
tf_minAc_3 = input.float(1.0, "MinZ", group=screener_group, inline='tf3')
tf_minSl_3 = input.float(10, "MinSl", group=screener_group, inline='tf3')
tf_rfMode_3 = input.int(1, "RF Mode", options=[-1,0,1], group=screener_group, inline='tf3')
tf_rfInv_3 = input.bool(false, "Invert", group=screener_group, inline='tf3')

tf_timeframe_4 = input.timeframe(array.get(tf_periods, 3), array.get(tf_names, 3), group=screener_group, inline='tf4')
tf_weight_4 = input.float(array.get(tf_default_weights, 3), "Weight", group=screener_group, inline='tf4')
tf_minUp_4 = input.int(2, "MinUp", group=screener_group, inline='tf4')
tf_minDn_4 = input.int(2, "MinDn", group=screener_group, inline='tf4')
tf_minAc_4 = input.float(1.0, "MinZ", group=screener_group, inline='tf4')
tf_minSl_4 = input.float(10, "MinSl", group=screener_group, inline='tf4')
tf_rfMode_4 = input.int(1, "RF Mode", options=[-1,0,1], group=screener_group, inline='tf4')
tf_rfInv_4 = input.bool(false, "Invert", group=screener_group, inline='tf4')

// Store timeframe settings in arrays for easy access
var bool[] tf_enabled = array.new_bool(4)
array.set(tf_enabled, 0, tf_enabled_1)
array.set(tf_enabled, 1, tf_enabled_2)
array.set(tf_enabled, 2, tf_enabled_3)
array.set(tf_enabled, 3, tf_enabled_4)

var string[] tf_timeframes = array.new_string(4)
array.set(tf_timeframes, 0, tf_timeframe_1)
array.set(tf_timeframes, 1, tf_timeframe_2)
array.set(tf_timeframes, 2, tf_timeframe_3)
array.set(tf_timeframes, 3, tf_timeframe_4)

var float[] tf_weights = array.new_float(4)
array.set(tf_weights, 0, tf_weight_1)
array.set(tf_weights, 1, tf_weight_2)
array.set(tf_weights, 2, tf_weight_3)
array.set(tf_weights, 3, tf_weight_4)

var int[] tf_minUps = array.new_int(4)
array.set(tf_minUps, 0, tf_minUp_1)
array.set(tf_minUps, 1, tf_minUp_2)
array.set(tf_minUps, 2, tf_minUp_3)
array.set(tf_minUps, 3, tf_minUp_4)

var int[] tf_minDns = array.new_int(4)
array.set(tf_minDns, 0, tf_minDn_1)
array.set(tf_minDns, 1, tf_minDn_2)
array.set(tf_minDns, 2, tf_minDn_3)
array.set(tf_minDns, 3, tf_minDn_4)

var float[] tf_minAcs = array.new_float(4)
array.set(tf_minAcs, 0, tf_minAc_1)
array.set(tf_minAcs, 1, tf_minAc_2)
array.set(tf_minAcs, 2, tf_minAc_3)
array.set(tf_minAcs, 3, tf_minAc_4)

var float[] tf_minSls = array.new_float(4)
array.set(tf_minSls, 0, tf_minSl_1)
array.set(tf_minSls, 1, tf_minSl_2)
array.set(tf_minSls, 2, tf_minSl_3)
array.set(tf_minSls, 3, tf_minSl_4)

var int[] tf_rfModes = array.new_int(4)
array.set(tf_rfModes, 0, tf_rfMode_1)
array.set(tf_rfModes, 1, tf_rfMode_2)
array.set(tf_rfModes, 2, tf_rfMode_3)
array.set(tf_rfModes, 3, tf_rfMode_4)

var bool[] tf_rfInvs = array.new_bool(4)
array.set(tf_rfInvs, 0, tf_rfInv_1)
array.set(tf_rfInvs, 1, tf_rfInv_2)
array.set(tf_rfInvs, 2, tf_rfInv_3)
array.set(tf_rfInvs, 3, tf_rfInv_4)

// Common inputs
sc_useDir = input.bool(true, "Use DI Direction", group=screener_group, inline="toggle")
sc_useAcc = input.bool(true, "Use Acceleration", group=screener_group, inline="toggle")
sc_useSlp = input.bool(true, "Use Slope", group=screener_group, inline="toggle")
sc_minValid = input.int(2, "Min Valid TFs", minval=1, group=screener_group)
sc_minAgree = input.int(3, "Min Direction Agree", minval=1, group=screener_group)
sc_minScore = input.float(4.0, "Min Composite Score", step=0.1, group=screener_group)

// Create TimeframeSettings objects using the "Features" pattern
TimeframeSettings tf1Settings = TimeframeSettings.new(
    tf1_enabled,
    tf1_timeframe,
    tf1_weight,
    tf1_minUp,
    tf1_minDn,
    tf1_minAc,
    tf1_minSl,
    tf1_rfMode,
    tf1_rfInv
)

TimeframeSettings tf2Settings = TimeframeSettings.new(
    tf2_enabled,
    tf2_timeframe,
    tf2_weight,
    tf2_minUp,
    tf2_minDn,
    tf2_minAc,
    tf2_minSl,
    tf2_rfMode,
    tf2_rfInv
)

TimeframeSettings tf3Settings = TimeframeSettings.new(
    tf3_enabled,
    tf3_timeframe,
    tf3_weight,
    tf3_minUp,
    tf3_minDn,
    tf3_minAc,
    tf3_minSl,
    tf3_rfMode,
    tf3_rfInv
)

TimeframeSettings tf4Settings = TimeframeSettings.new(
    tf4_enabled,
    tf4_timeframe,
    tf4_weight,
    tf4_minUp,
    tf4_minDn,
    tf4_minAc,
    tf4_minSl,
    tf4_rfMode,
    tf4_rfInv
)
// Config objects
cfgRange cfgRange = cfgRange.new(
    rf_src,
    s(rf_period),
    s(rf_smoothing),
    s(rf_cmoPeriod),
    rf_multDefault,
    rf_multBull,
    rf_cmoBull,
    rf_multNeut,
    rf_cmoNeut,
    rf_multBear,
    rf_cmoBear,
    s(rf_lookback))

cfgAdx cfgAdx = cfgAdx.new(
    adx_useFilter,
    s(adx_len),
    s(adx_threshold),
    s(adx_upperLimit),
    s(adx_lowerLimit),
    adx_dynLimits,
    s(adx_cmoPeriod),
    adx_colorLimits,
    adx_colorDefault)

cfgDisplay cfgDisplay = cfgDisplay.new(
    disp_trend_enable,
    disp_limit_enable,
    disp_slope_enable,
    disp_slope_thresh,
    disp_slope_disp,
    disp_slope_upColor,
    disp_slope_dnColor,
    disp_slope_neuColor,
    s(disp_slope_width))

// Simplified cfgScreener object using the "Features" pattern
cfgScreener cfgScreener = cfgScreener.new(
    array.get(tf_enabled, 0),
    array.get(tf_enabled, 1),
    array.get(tf_enabled, 2),
    array.get(tf_enabled, 3),
    array.get(tf_timeframes, 0),
    array.get(tf_timeframes, 1),
    array.get(tf_timeframes, 2),
    array.get(tf_timeframes, 3),
    array.get(tf_weights, 0),
    array.get(tf_weights, 1),
    array.get(tf_weights, 2),
    array.get(tf_weights, 3),
    sc_useDir,
    sc_useAcc,
    sc_useSlp,
    s(array.get(tf_minUps, 0)),
    s(array.get(tf_minDns, 0)),
    array.get(tf_minAcs, 0),
    array.get(tf_minSls, 0),
    s(array.get(tf_rfModes, 0)),
    array.get(tf_rfInvs, 0),
    s(array.get(tf_minUps, 1)),
    s(array.get(tf_minDns, 1)),
    array.get(tf_minAcs, 1),
    array.get(tf_minSls, 1),
    s(array.get(tf_rfModes, 1)),
    array.get(tf_rfInvs, 1),
    s(array.get(tf_minUps, 2)),
    s(array.get(tf_minDns, 2)),
    array.get(tf_minAcs, 2),
    array.get(tf_minSls, 2),
    s(array.get(tf_rfModes, 2)),
    array.get(tf_rfInvs, 2),
    s(array.get(tf_minUps, 3)),
    s(array.get(tf_minDns, 3)),
    array.get(tf_minAcs, 3),
    array.get(tf_minSls, 3),
    s(array.get(tf_rfModes, 3)),
    array.get(tf_rfInvs, 3),
    s(sc_minValid),
    s(sc_minAgree),
    sc_minScore)

// Helper functions with optimized structure using the "Features" pattern
// These functions access the timeframe settings arrays directly

// Get a specific value from the timeframe settings arrays by index
getConfigValue(valueType, index) =>
    if valueType == "minUp"
        array.get(tf_minUps, index)
    else if valueType == "minDn"
        array.get(tf_minDns, index)
    else if valueType == "minAc"
        array.get(tf_minAcs, index)
    else if valueType == "minSl"
        array.get(tf_minSls, index)
    else if valueType == "rfMode"
        array.get(tf_rfModes, index)
    else if valueType == "rfInvert"
        array.get(tf_rfInvs, index)
    else if valueType == "weight"
        array.get(tf_weights, index)
    else na

// Check if a timeframe is enabled
isTfEnabled(index) =>
    array.get(tf_enabled, index)

// Get the timeframe string for a specific index
getTfString(index) =>
    array.get(tf_timeframes, index)

// Initialize weights array
for i = 0 to 3
    array.set(weights, i, getConfigValue("weight", i))
// Core functions - optimized for conciseness
f_zscore(_src, _length) =>
    _mean = ta.sma(_src, _length), _std = ta.stdev(_src - _mean, _length)
    _std != 0 ? (_src - _mean) / _std : 0

f_selectMultiplier(_z, _z1, _z2, _z3, _m0, _m1, _m2, _m3) =>
    _z > _z1 ? _m1 : _z > _z2 ? _m2 : _z < _z3 ? _m3 : _m0

f_calcADX(adxL, _high=high, _low=low, _close=close) =>
    tr = math.max(math.max(_high - _low, math.abs(_high - nz(_close[1]))), math.abs(_low - nz(_close[1])))
    upMove = _high - nz(_high[1], _high), downMove = nz(_low[1], _low) - _low
    plusDM = upMove > downMove and upMove > 0 ? upMove : 0
    minusDM = downMove > upMove and downMove > 0 ? downMove : 0
    trSum = ta.rma(tr, adxL)
    plusDI = trSum != 0 ? 100 * ta.rma(plusDM, adxL) / trSum : 0
    minusDI = trSum != 0 ? 100 * ta.rma(minusDM, adxL) / trSum : 0
    dx = (plusDI + minusDI) != 0 ? 100 * math.abs(plusDI - minusDI) / (plusDI + minusDI) : 0
    adx = ta.rma(dx, adxL)
    [adx, plusDI, minusDI]

f_calcAdxLimits(_adx, _useDyn, _up, _lo, _len) =>
    hi = ta.highest(_adx, _len)
    lo = ta.lowest(_adx, _len)
    rng = hi - lo
    up = _useDyn ? lo + _up * rng / 100 : _up
    lw = _useDyn ? lo + _lo * rng / 100 : _lo
    ctr = (up + lw) / 2
    clr = _adx > up ? color.green : _adx < lw ? color.red : color.gray
    [up, lw, ctr, clr]

f_smoothRange(_src, _smth, _period, _zPeriod, _bullF, _neutF, _bearF, _defM, _bullM, _neutM, _bearM, _cmoPeriod) =>
    avrng = ta.ema(math.abs(_src - nz(_src[1], _src)), _smth)
    cmoRaw = ta.cmo(_src, _cmoPeriod)
    z = f_zscore(cmoRaw, _zPeriod)
    multRes = f_selectMultiplier(z, _bullF, _neutF, _bearF, _defM, _bullM, _neutM, _bearM)
    wper = _smth * 2 - 1
    smooth = ta.ema(avrng, wper) * multRes
    [smooth, z, multRes, cmoRaw]

f_rangeFilter(_src, _range, _prev) =>
    _src > nz(_prev, _src) ? math.max(nz(_prev, _src), _src - _range) : math.min(nz(_prev, _src), _src + _range)

f_updateRangeFilter(_src, _smoothed, _prevRfLine, _prevRfUp, _prevRfDn) =>
    rfLine = f_rangeFilter(_src, _smoothed, _prevRfLine)
    rfUp = rfLine > _prevRfLine ? _prevRfUp + 1 : rfLine < _prevRfLine ? 0 : _prevRfUp
    rfDn = rfLine < _prevRfLine ? _prevRfDn + 1 : rfLine > _prevRfLine ? 0 : _prevRfDn
    [rfLine, rfUp, rfDn]

f_calcSlope(_src, _thres, _disp, _upColor, _downColor, _neutColor) =>
    slope = math.atan((_src - nz(_src[1], _src)) / 2.0) * 180 / math.pi
    pos = _src + _disp
    color = slope > _thres ? _downColor : slope < -_thres ? _upColor : _neutColor
    [pos, color, slope]

f_getCompositeTrendState(_adx, _plusDI, _minusDI, _slope, _zscore, _rfUp, _rfDn, _cfg, _i) =>
    upMin = getConfigValue("minUp", _i-1)
    dnMin = getConfigValue("minDn", _i-1)
    accMin = getConfigValue("minAc", _i-1)
    slopeMin = getConfigValue("minSl", _i-1)
    rfMode = getConfigValue("rfMode", _i-1)
    rfInvert = getConfigValue("rfInvert", _i-1)

    dir = _plusDI > _minusDI ? 1 : _plusDI < _minusDI ? -1 : 0
    int rfState = rfMode == 1 ? (_rfUp >= upMin ? 1 : 0) : rfMode == -1 ? (_rfDn >= dnMin ? -1 : 0) : 0
    rfState := rfInvert ? -rfState : rfState

    rfValid = rfState != 0
    accValid = not _cfg.minAcc_tf or math.abs(_zscore) >= accMin
    slopeValid = not _cfg.minSlp_tf or math.abs(_slope) >= slopeMin
    dirValid = not _cfg.minDir_tf or dir != 0
    isValid = rfValid and accValid and slopeValid and dirValid

    state = _adx > cfgAdx.upperLimit ? StrongTrend : _adx > cfgAdx.lowerLimit ? WeakTrend : NoTrend
    [state, dir, isValid, rfValid]

f_mtfLogic(_src, _cfgRange, adxLen, _cfgDisplay, _prevRfLine, _prevRfUp, _prevRfDn) =>
    [smoothed, zscore, mult, cmoRaw] = f_smoothRange(_src, _cfgRange.smoothing, _cfgRange.period,
        _cfgRange.cmoPeriod, _cfgRange.bullCMO, _cfgRange.neutCMO, _cfgRange.bearCMO,
        _cfgRange.defaultMult, _cfgRange.bullMult, _cfgRange.neutMult, _cfgRange.bearMult, _cfgRange.look)
    [rfLine, rfUp, rfDn] = f_updateRangeFilter(_src, smoothed, _prevRfLine, _prevRfUp, _prevRfDn)
    [adx, plusDI, minusDI] = f_calcADX(adxLen, high, low, close)
    [_, _, slope] = f_calcSlope(adx, _cfgDisplay.slopeThreshold, _cfgDisplay.slopeDisplacement,
        _cfgDisplay.colorSlopeUp, _cfgDisplay.colorSlopeDown, _cfgDisplay.colorSlopeNeutral)
    MtfScreenerResult.new(rfLine, adx, plusDI, minusDI, slope, zscore, rfUp, rfDn, mult)

// Process a single timeframe using the "Features" pattern
processMTF(index) =>
    if array.get(tf_enabled, index)
        prevRfLine = array.get(rfLines, index)
        prevRfUp = array.get(rfUps, index)
        prevRfDn = array.get(rfDns, index)

        result = request.security(
            syminfo.tickerid,
            array.get(tf_timeframes, index),
            f_mtfLogic(cfgRange.src, cfgRange, cfgAdx.length, cfgDisplay, prevRfLine, prevRfUp, prevRfDn)
        )

        if not na(result)
            array.set(rfLines, index, result.rangeFilter)
            array.set(rfUps, index, result.rangeUpCount)
            array.set(rfDns, index, result.rangeDownCount)
            array.set(adxVals, index, result.adxValue)
            array.set(plusDIs, index, result.adxPlusDI)
            array.set(minusDIs, index, result.adxMinusDI)
            array.set(adxSlopes, index, result.adxSlope)
            array.set(adxZscores, index, result.adxZscore)
            array.set(mults, index, result.mult)
        result
    else na
// Process all timeframes using the "Features" pattern
result_tf1 = processMTF(0)
result_tf2 = processMTF(1)
result_tf3 = processMTF(2)
result_tf4 = processMTF(3)

generateSignal(validCount, directionSum, compositeScore, cfg) =>
    isValid = validCount >= cfg.minValidCount
    hasDirection = math.abs(directionSum) >= cfg.minDirectionAgreement
    hasStrength = compositeScore >= cfg.minCompositeScore
    signal = isValid and hasDirection and hasStrength
    direction = directionSum > 0 ? 1 : directionSum < 0 ? -1 : 0
    [signal, direction]

// Reset composite metrics for this bar
validCount := 0
directionSum := 0
compositeScore := 0.0

// Process each timeframe
for i = 0 to 3
    if isTfEnabled(i)
        rfUp = array.get(rfUps, i)
        rfDn = array.get(rfDns, i)
        adxVal = array.get(adxVals, i)
        plusDI = array.get(plusDIs, i)
        minusDI = array.get(minusDIs, i)
        slope = array.get(adxSlopes, i)
        zscore = array.get(adxZscores, i)
        upMin = getConfigValue("minUp", i)
        dnMin = getConfigValue("minDn", i)
        accMin = getConfigValue("minAc", i)
        slopeMin = getConfigValue("minSl", i)
        rfMode = getConfigValue("rfMode", i)
        rfInvert = getConfigValue("rfInvert", i)
        weight = getConfigValue("weight", i)

        // Calculate state
        int rfState = rfMode == 1 ? (rfUp >= upMin ? 1 : 0) : rfMode == -1 ? (rfDn >= dnMin ? -1 : 0) : 0
        rfState := rfInvert ? -rfState : rfState
        int dir = plusDI > minusDI ? 1 : (plusDI < minusDI ? -1 : 0)

        // Validate conditions
        bool rfValid = rfState != 0
        bool accValid = not cfgScreener.minAcc_tf or math.abs(zscore) >= accMin
        bool slopeValid = not cfgScreener.minSlp_tf or math.abs(slope) >= slopeMin
        bool dirValid = not cfgScreener.minDir_tf or dir != 0
        int state = adxVal > cfgAdx.upperLimit ? StrongTrend : adxVal > cfgAdx.lowerLimit ? WeakTrend : NoTrend

        // Store results
        array.set(states, i, state)
        array.set(directions, i, dir)
        array.set(validFlags, i, rfValid)

        // Update composite metrics
        bool isValid = rfValid and accValid and slopeValid and dirValid
        if isValid
            validCount := validCount + 1
            directionSum := directionSum + dir
            compositeScore := compositeScore + weight * math.abs(dir)

// Generate final signal
[entrySignal, signalDir] = generateSignal(validCount, directionSum, compositeScore, cfgScreener)
// Plotting

// Calculate plot values for all timeframes using arrays
var bool[] tf_enabled = array.new_bool(4, false)
var int[] tf_state = array.new_int(4, na)
var bool[] tf_rfValid = array.new_bool(4, false)
var float[] tf_zscore = array.new_float(4, na)
var float[] tf_slope = array.new_float(4, na)
var float[] tf_minAc = array.new_float(4, na)
var float[] tf_minSl = array.new_float(4, na)

// Pre-calculate y-positions for each timeframe and indicator
var float tf1_base = -20
var float tf2_base = -35
var float tf3_base = -50
var float tf4_base = -65

// Fill arrays with values for each timeframe
tf_enabled_0 = isTfEnabled(0)
tf_enabled_1 = isTfEnabled(1)
tf_enabled_2 = isTfEnabled(2)
tf_enabled_3 = isTfEnabled(3)

// TF1 values
tf1_state = tf_enabled_0 ? array.get(states, 0) : na
tf1_rfValid = tf_enabled_0 ? array.get(validFlags, 0) : na
tf1_zscore = tf_enabled_0 ? array.get(adxZscores, 0) : na
tf1_slope = tf_enabled_0 ? array.get(adxSlopes, 0) : na
tf1_minAc = tf_enabled_0 ? getConfigValue("minAc", 0) : na
tf1_minSl = tf_enabled_0 ? getConfigValue("minSl", 0) : na

// TF2 values
tf2_state = tf_enabled_1 ? array.get(states, 1) : na
tf2_rfValid = tf_enabled_1 ? array.get(validFlags, 1) : na
tf2_zscore = tf_enabled_1 ? array.get(adxZscores, 1) : na
tf2_slope = tf_enabled_1 ? array.get(adxSlopes, 1) : na
tf2_minAc = tf_enabled_1 ? getConfigValue("minAc", 1) : na
tf2_minSl = tf_enabled_1 ? getConfigValue("minSl", 1) : na

// TF3 values
tf3_state = tf_enabled_2 ? array.get(states, 2) : na
tf3_rfValid = tf_enabled_2 ? array.get(validFlags, 2) : na
tf3_zscore = tf_enabled_2 ? array.get(adxZscores, 2) : na
tf3_slope = tf_enabled_2 ? array.get(adxSlopes, 2) : na
tf3_minAc = tf_enabled_2 ? getConfigValue("minAc", 2) : na
tf3_minSl = tf_enabled_2 ? getConfigValue("minSl", 2) : na

// TF4 values
tf4_state = tf_enabled_3 ? array.get(states, 3) : na
tf4_rfValid = tf_enabled_3 ? array.get(validFlags, 3) : na
tf4_zscore = tf_enabled_3 ? array.get(adxZscores, 3) : na
tf4_slope = tf_enabled_3 ? array.get(adxSlopes, 3) : na
tf4_minAc = tf_enabled_3 ? getConfigValue("minAc", 3) : na
tf4_minSl = tf_enabled_3 ? getConfigValue("minSl", 3) : na

// TF1 plots - all at global scope
plot(tf_enabled_0 and tf1_state == StrongTrend ? tf1_base : na, title="TF1 Trend Strong", style=plot.style_circles, color=color.green)
plot(tf_enabled_0 and tf1_state == WeakTrend ? tf1_base : na, title="TF1 Trend Weak", style=plot.style_circles, color=color.orange)
plot(tf_enabled_0 and tf1_state == NoTrend ? tf1_base : na, title="TF1 Trend None", style=plot.style_circles, color=color.red)
plot(tf_enabled_0 and tf1_rfValid ? tf1_base - 3 : na, title="TF1 RF Valid", style=plot.style_circles, color=color.new(color.green, 0))
plot(tf_enabled_0 and math.abs(tf1_zscore) >= tf1_minAc ? tf1_base - 6 : na, title="TF1 Zscore OK", style=plot.style_circles, color=color.teal)
plot(tf_enabled_0 and math.abs(tf1_slope) >= tf1_minSl ? tf1_base - 9 : na, title="TF1 Slope OK", style=plot.style_circles, color=color.blue)
plot(tf_enabled_0 ? tf1_base - 12 : na, title="Separator TF1", color=color.gray, style=plot.style_linebr)

// TF2 plots - all at global scope
plot(tf_enabled_1 and tf2_state == StrongTrend ? tf2_base : na, title="TF2 Trend Strong", style=plot.style_circles, color=color.green)
plot(tf_enabled_1 and tf2_state == WeakTrend ? tf2_base : na, title="TF2 Trend Weak", style=plot.style_circles, color=color.orange)
plot(tf_enabled_1 and tf2_state == NoTrend ? tf2_base : na, title="TF2 Trend None", style=plot.style_circles, color=color.red)
plot(tf_enabled_1 and tf2_rfValid ? tf2_base - 3 : na, title="TF2 RF Valid", style=plot.style_circles, color=color.new(color.green, 0))
plot(tf_enabled_1 and math.abs(tf2_zscore) >= tf2_minAc ? tf2_base - 6 : na, title="TF2 Zscore OK", style=plot.style_circles, color=color.teal)
plot(tf_enabled_1 and math.abs(tf2_slope) >= tf2_minSl ? tf2_base - 9 : na, title="TF2 Slope OK", style=plot.style_circles, color=color.blue)
plot(tf_enabled_1 ? tf2_base - 12 : na, title="Separator TF2", color=color.gray, style=plot.style_linebr)

// TF3 plots - all at global scope
plot(tf_enabled_2 and tf3_state == StrongTrend ? tf3_base : na, title="TF3 Trend Strong", style=plot.style_circles, color=color.green)
plot(tf_enabled_2 and tf3_state == WeakTrend ? tf3_base : na, title="TF3 Trend Weak", style=plot.style_circles, color=color.orange)
plot(tf_enabled_2 and tf3_state == NoTrend ? tf3_base : na, title="TF3 Trend None", style=plot.style_circles, color=color.red)
plot(tf_enabled_2 and tf3_rfValid ? tf3_base - 3 : na, title="TF3 RF Valid", style=plot.style_circles, color=color.new(color.green, 0))
plot(tf_enabled_2 and math.abs(tf3_zscore) >= tf3_minAc ? tf3_base - 6 : na, title="TF3 Zscore OK", style=plot.style_circles, color=color.teal)
plot(tf_enabled_2 and math.abs(tf3_slope) >= tf3_minSl ? tf3_base - 9 : na, title="TF3 Slope OK", style=plot.style_circles, color=color.blue)
plot(tf_enabled_2 ? tf3_base - 12 : na, title="Separator TF3", color=color.gray, style=plot.style_linebr)

// TF4 plots - all at global scope
plot(tf_enabled_3 and tf4_state == StrongTrend ? tf4_base : na, title="TF4 Trend Strong", style=plot.style_circles, color=color.green)
plot(tf_enabled_3 and tf4_state == WeakTrend ? tf4_base : na, title="TF4 Trend Weak", style=plot.style_circles, color=color.orange)
plot(tf_enabled_3 and tf4_state == NoTrend ? tf4_base : na, title="TF4 Trend None", style=plot.style_circles, color=color.red)
plot(tf_enabled_3 and tf4_rfValid ? tf4_base - 3 : na, title="TF4 RF Valid", style=plot.style_circles, color=color.new(color.green, 0))
plot(tf_enabled_3 and math.abs(tf4_zscore) >= tf4_minAc ? tf4_base - 6 : na, title="TF4 Zscore OK", style=plot.style_circles, color=color.teal)
plot(tf_enabled_3 and math.abs(tf4_slope) >= tf4_minSl ? tf4_base - 9 : na, title="TF4 Slope OK", style=plot.style_circles, color=color.blue)
plot(tf_enabled_3 ? tf4_base - 12 : na, title="Separator TF4", color=color.gray, style=plot.style_linebr)

// === Final Signal Plot
plot(entrySignal ? -80 : na, title="Entry Signal", style=plot.style_circles, color=color.lime)

// === Range Filter Lines per TF
plot(array.get(rfLines, 0), title="RF TF1", color=color.lime, linewidth=2, force_overlay = true)
plot(array.get(rfLines, 1), title="RF TF2", color=color.red, linewidth=2, force_overlay = true)
plot(array.get(rfLines, 2), title="RF TF3", color=color.orange, linewidth=2, force_overlay = true)
plot(array.get(rfLines, 3), title="RF TF4", color=color.aqua, linewidth=2, force_overlay = true)

// === ADX Visuals (non-overlay)
plot(array.get(adxVals, 0), title="ADX TF1", color=color.blue, linewidth=1, display=display.none)
plot(array.get(adxVals, 1), title="ADX TF2", color=color.orange, linewidth=1, display=display.none)
plot(array.get(adxVals, 2), title="ADX TF3", color=color.fuchsia, linewidth=1, display=display.none)
plot(array.get(adxVals, 3), title="ADX TF4", color=color.navy, linewidth=1, display=display.none)
mult = array.get(mults, 0)

zColor = mult == cfgRange.bullMult ? color.rgb(76, 175, 79, 50) :
         mult == cfgRange.neutMult ? color.rgb(255, 235, 59, 50) :
         mult == cfgRange.bearMult ? color.rgb(255, 152, 0, 50) :
         color.rgb(255, 82, 82, 50)

[_, zCMO, _, cmoRaw] = f_smoothRange(
    cfgRange.src, s(rf_smoothing), s(rf_period), s(rf_cmoPeriod),
    cfgRange.bullCMO, cfgRange.neutCMO, cfgRange.bearCMO,
    cfgRange.defaultMult, cfgRange.bullMult, cfgRange.neutMult, cfgRange.bearMult, s(rf_lookback))

plot(zCMO, title="CMO Z", color=zColor, linewidth=2, style=plot.style_columns)

