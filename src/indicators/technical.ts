import { OHLCV } from '../types';

/**
 * Technical Analysis Indicators
 * Converted from PineScript to TypeScript with proper mathematical implementations
 */

export class TechnicalIndicators {
  
  /**
   * Calculate Simple Moving Average
   */
  static sma(values: number[], period: number): number[] {
    const result: number[] = [];
    for (let i = 0; i < values.length; i++) {
      if (i < period - 1) {
        result.push(NaN);
      } else {
        const sum = values.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
        result.push(sum / period);
      }
    }
    return result;
  }

  /**
   * Calculate Exponential Moving Average
   */
  static ema(values: number[], period: number): number[] {
    const result: number[] = [];
    const multiplier = 2 / (period + 1);
    
    for (let i = 0; i < values.length; i++) {
      if (i === 0) {
        result.push(values[i]);
      } else {
        result.push((values[i] * multiplier) + (result[i - 1] * (1 - multiplier)));
      }
    }
    return result;
  }

  /**
   * Calculate Relative Moving Average (RMA) - equivalent to PineScript ta.rma
   */
  static rma(values: number[], period: number): number[] {
    const result: number[] = [];
    const alpha = 1 / period;
    
    for (let i = 0; i < values.length; i++) {
      if (i === 0) {
        result.push(values[i]);
      } else {
        result.push(alpha * values[i] + (1 - alpha) * result[i - 1]);
      }
    }
    return result;
  }

  /**
   * Calculate Standard Deviation
   */
  static stdev(values: number[], period: number): number[] {
    const result: number[] = [];
    const smaValues = this.sma(values, period);
    
    for (let i = 0; i < values.length; i++) {
      if (i < period - 1) {
        result.push(NaN);
      } else {
        const mean = smaValues[i];
        const variance = values.slice(i - period + 1, i + 1)
          .reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / period;
        result.push(Math.sqrt(variance));
      }
    }
    return result;
  }

  /**
   * Calculate Average True Range (ATR)
   */
  static atr(data: OHLCV[], period: number): number[] {
    const trueRanges: number[] = [];
    
    for (let i = 0; i < data.length; i++) {
      if (i === 0) {
        trueRanges.push(data[i].high - data[i].low);
      } else {
        const tr = Math.max(
          data[i].high - data[i].low,
          Math.abs(data[i].high - data[i - 1].close),
          Math.abs(data[i].low - data[i - 1].close)
        );
        trueRanges.push(tr);
      }
    }
    
    return this.rma(trueRanges, period);
  }

  /**
   * Calculate Directional Movement Index (DMI) - returns [ADX, +DI, -DI]
   */
  static dmi(data: OHLCV[], period: number): { adx: number[], plusDI: number[], minusDI: number[] } {
    const trueRanges: number[] = [];
    const plusDM: number[] = [];
    const minusDM: number[] = [];
    
    for (let i = 0; i < data.length; i++) {
      if (i === 0) {
        trueRanges.push(data[i].high - data[i].low);
        plusDM.push(0);
        minusDM.push(0);
      } else {
        // True Range
        const tr = Math.max(
          data[i].high - data[i].low,
          Math.abs(data[i].high - data[i - 1].close),
          Math.abs(data[i].low - data[i - 1].close)
        );
        trueRanges.push(tr);
        
        // Directional Movement
        const upMove = data[i].high - data[i - 1].high;
        const downMove = data[i - 1].low - data[i].low;
        
        plusDM.push(upMove > downMove && upMove > 0 ? upMove : 0);
        minusDM.push(downMove > upMove && downMove > 0 ? downMove : 0);
      }
    }
    
    const trSum = this.rma(trueRanges, period);
    const plusDMSum = this.rma(plusDM, period);
    const minusDMSum = this.rma(minusDM, period);
    
    const plusDI: number[] = [];
    const minusDI: number[] = [];
    const dx: number[] = [];
    
    for (let i = 0; i < data.length; i++) {
      if (trSum[i] && !isNaN(trSum[i]) && trSum[i] !== 0) {
        const pdi = 100 * plusDMSum[i] / trSum[i];
        const mdi = 100 * minusDMSum[i] / trSum[i];
        plusDI.push(pdi);
        minusDI.push(mdi);
        
        const dxValue = pdi + mdi !== 0 ? 100 * Math.abs(pdi - mdi) / (pdi + mdi) : 0;
        dx.push(dxValue);
      } else {
        plusDI.push(NaN);
        minusDI.push(NaN);
        dx.push(NaN);
      }
    }
    
    const adx = this.rma(dx, period);
    
    return { adx, plusDI, minusDI };
  }

  /**
   * Calculate Chande Momentum Oscillator (CMO)
   */
  static cmo(values: number[], period: number): number[] {
    const result: number[] = [];
    
    for (let i = 0; i < values.length; i++) {
      if (i < period) {
        result.push(NaN);
      } else {
        let sumUp = 0;
        let sumDown = 0;
        
        for (let j = i - period + 1; j <= i; j++) {
          const change = values[j] - values[j - 1];
          if (change > 0) {
            sumUp += change;
          } else {
            sumDown += Math.abs(change);
          }
        }
        
        const cmoValue = sumUp + sumDown !== 0 ? 100 * (sumUp - sumDown) / (sumUp + sumDown) : 0;
        result.push(cmoValue);
      }
    }
    
    return result;
  }

  /**
   * Calculate Rate of Change (ROC)
   */
  static roc(values: number[], period: number): number[] {
    const result: number[] = [];
    
    for (let i = 0; i < values.length; i++) {
      if (i < period) {
        result.push(NaN);
      } else {
        const roc = values[i - period] !== 0 ? 
          ((values[i] - values[i - period]) / values[i - period]) * 100 : 0;
        result.push(roc);
      }
    }
    
    return result;
  }

  /**
   * Calculate Correlation between two series
   */
  static correlation(series1: number[], series2: number[], period: number): number[] {
    const result: number[] = [];
    
    for (let i = 0; i < series1.length; i++) {
      if (i < period - 1) {
        result.push(NaN);
      } else {
        const x = series1.slice(i - period + 1, i + 1);
        const y = series2.slice(i - period + 1, i + 1);
        
        const meanX = x.reduce((a, b) => a + b, 0) / period;
        const meanY = y.reduce((a, b) => a + b, 0) / period;
        
        let numerator = 0;
        let sumXSquared = 0;
        let sumYSquared = 0;
        
        for (let j = 0; j < period; j++) {
          const xDiff = x[j] - meanX;
          const yDiff = y[j] - meanY;
          numerator += xDiff * yDiff;
          sumXSquared += xDiff * xDiff;
          sumYSquared += yDiff * yDiff;
        }
        
        const denominator = Math.sqrt(sumXSquared * sumYSquared);
        const correlation = denominator !== 0 ? numerator / denominator : 0;
        result.push(correlation);
      }
    }
    
    return result;
  }

  /**
   * Get highest value in period
   */
  static highest(values: number[], period: number): number[] {
    const result: number[] = [];
    
    for (let i = 0; i < values.length; i++) {
      if (i < period - 1) {
        result.push(NaN);
      } else {
        const slice = values.slice(i - period + 1, i + 1);
        result.push(Math.max(...slice));
      }
    }
    
    return result;
  }

  /**
   * Get lowest value in period
   */
  static lowest(values: number[], period: number): number[] {
    const result: number[] = [];
    
    for (let i = 0; i < values.length; i++) {
      if (i < period - 1) {
        result.push(NaN);
      } else {
        const slice = values.slice(i - period + 1, i + 1);
        result.push(Math.min(...slice));
      }
    }
    
    return result;
  }
}
