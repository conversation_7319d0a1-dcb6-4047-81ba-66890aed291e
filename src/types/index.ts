// Core trading system types
export interface OHLCV {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface RangeFilterSettings {
  rfSrc: 'close' | 'open' | 'high' | 'low';
  rfPeriod: number;
  rfSmthLen: number;
  cmoZPeriod: number;
  rangeMult0: number;
  rangeMulti1: number;
  cmoBullZ: number;
  rangeMulti2: number;
  cmoNeutZ: number;
  rangeMulti3: number;
  cmoBearZ: number;
}

export interface AdxSettings {
  useAdxFilt: boolean;
  adxLen: number;
  adxTh: number;
  upperLimit: number;
  lowerLimit: number;
  isDynamicLimits: boolean;
  minMaxPeriod: number;
}

export interface SignalGenerationSettings {
  enableSignals: boolean;
  confidenceThreshold: number;
  regimeSensitivity: number;
  mtfWeightDecay: number;
}

export interface RiskManagementSettings {
  stopLossAtrMult: number;
  takeProfitRatio: number;
  maxPositionSize: number;
  dynamicSizing: boolean;
  maxDailyLossPct: number;
  maxConsecutiveLosses: number;
  volatilityFilterEnabled: boolean;
  volatilityZscoreThreshold: number;
  liquidityFilterEnabled: boolean;
  minVolumeMultiplier: number;
}

export interface MTFSignalData {
  adxValue: number;
  adxSlope: number;
  adxAccel: number;
  rfValue: number;
  rfSlope: number;
  rfAccel: number;
  cmoZ: number;
  volumeWeight: number;
  rfBullish: boolean;
  adxStrong: boolean;
  timeframeWeight: number;
}

export interface ConfluenceMatrix {
  adxAlignment: number;
  rfMomentum: number;
  cmoRegime: 'trending' | 'transitional' | 'ranging';
  volumeConfirmation: number;
  slopeConvergence: number;
  accelerationDivergence: number;
}

export interface SignalOutput {
  buySignal: boolean;
  sellSignal: boolean;
  confidence: number;
  entryPrice: number;
  stopLoss: number;
  takeProfit: number;
  regime: string;
  positionSize: number;
  baseStrength: number;
  volRegime: string;
  liquidityScore: number;
  btcCorrelation: number;
}

export interface MarketConditions {
  volatilityZscore: number;
  extremeVolatility: boolean;
  volatilityRegime: 'high' | 'normal' | 'low';
  volumeRatio: number;
  lowLiquidity: boolean;
  wideSpread: boolean;
  liquidityScore: number;
  btcCorrelation: number;
  correlationOk: boolean;
  marketStructureBullish: boolean;
  marketStructureBearish: boolean;
}

export interface RiskControls {
  dailyPnl: number;
  consecutiveLosses: number;
  portfolioHeat: number;
  circuitBreakerActive: boolean;
  dailyLossExceeded: boolean;
  lossMultiplier: number;
  riskBudgetAvailable: boolean;
}

export interface TradeSignal {
  timestamp: number;
  symbol: string;
  side: 'buy' | 'sell';
  confidence: number;
  entryPrice: number;
  stopLoss: number;
  takeProfit: number;
  positionSize: number;
  regime: string;
  marketConditions: MarketConditions;
  riskControls: RiskControls;
}

export interface BacktestResult {
  totalTrades: number;
  winRate: number;
  totalReturn: number;
  maxDrawdown: number;
  sharpeRatio: number;
  profitFactor: number;
  avgWin: number;
  avgLoss: number;
  trades: TradeSignal[];
}

export type Timeframe = '1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1d';

export interface TimeframeData {
  timeframe: Timeframe;
  data: OHLCV[];
  signals: MTFSignalData;
}
