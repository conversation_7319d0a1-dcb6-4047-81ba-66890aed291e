// © cryptokairo
//@version=5
strategy("S&K ETH/USDT 15m Advanced MTF Signal Generator", overlay=false,
  commission_type = strategy.commission.percent, commission_value = 0.00,
  default_qty_type = strategy.percent_of_equity, default_qty_value = 100,
  initial_capital = 2000, slippage = 0, calc_on_every_tick = true, process_orders_on_close = false
  )
COLOR_GRAY           = color.rgb(87, 89, 94)
COLOR_BLUE           = color.rgb(64, 118, 179)
COLOR_TREND_BULL     = color.rgb(16 , 228, 217)
COLOR_TREND_BEAR     = color.rgb(201, 206, 63)
COLOR_SUPPLY         = color.rgb(170, 0  , 10)
COLOR_DEMAND         = color.rgb(0  , 190, 120)
COLOR_SLOPE_UP       = color.rgb(147, 212, 68)
COLOR_SLOPE_DOWN     = color.rgb(206, 60 , 140)
COLOR_SLOPE_NEUTRAL  = color.rgb(165, 165, 165)
COLOR_CMO_M1         = color.rgb(76, 175, 79, 50)
COLOR_CMO_M2         = color.rgb(255, 235, 59, 50)
COLOR_CMO_M3         = color.rgb(255, 152, 0, 50)
COLOR_CM0_M0         = color.rgb(255, 82, 82, 50)

var int condIni = 0
var const string visualTooltip      = "Visual Control Only"
var const string baseADX            = "Base ADX Condition, is true if ADX > Threshold"
var const string dynSLTooltip       = "ADX Long Threshold used for Tiered Stop Loss"
var const string adxLongPlotTooltip = "Toggle ADX Long used to determine if the Condition 'ADX Long > ADX Long Threshold' is true"
var const string upperLimitDescrp   = "Upper limit for ADX indicator"
var const string lowerLimitDescrp   = "Lower limit for ADX indicator"
var const string minMaxPeriodDesc   = "cmoLen period for ADX slope Tiered limits calculation"
var const string adxSlopeThresDesc  = "Threshold for ADX slope detection"
var const string adxSlopeDispDescrp = "Displacement for slope dots"
var const string setPNLTooltip      = "Profit level that must be reached before tracking retracement"
var const string deltaPNLTooltip    = "Amount of retracement from max profit that will trigger exit"
var const string rngcmoLenTooltip   = "Range cmoLen in period for the range filter multiplier calculation"
var const string rngSmthLenTooltip  = "Range smoothing length"
var const string defMultiTooltip    = "Default multiplier used for the CMO to determine current multiplier for the Smoothed Range Calculation"
var const string rngMulti1Tooltip   = "RF Multi 1 to be used if CMO < cmoBullZ to set the current multiplier for the Smoothed Range Calculation"
var const string rngMulti2Tooltip   = "RF Multi 2 to be used if CMO < cmoNeutZ to set the current multiplier for the Smoothed Range Calculation"
var const string rngMulti3Tooltip   = "RF Multi 3 to be used if CMO < cmoNeutZ to set the current multiplier for the Smoothed Range Calculation"
var const string cmoBullZTooltip    = rngMulti1Tooltip
var const string cmoNeutZTooltip    = rngMulti2Tooltip
var const string cmoBearZTooltip    = rngMulti3Tooltip

src = input(defval=close, title='Default Source')

type RangeFilterSettings
    float   rfSrc
    int     rfPeriod
    int     rfSmthLen
    int     cmoZPeriod
    float   rangeMult0
    float   rangeMulti1
    float   cmoBullZ
    float   rangeMulti2
    float   cmoNeutZ
    float   rangeMulti3
    float   cmoBearZ

rf_period       =   input.int  (defval=34       , minval=1      , title='Range Period'       , tooltip = rngcmoLenTooltip   , group='Range Filter'  )
rf_smoothing    =   input.int  (defval=78       , minval=1      , title='Smoothing Length'   , tooltip = rngSmthLenTooltip  , group='Range Filter'  )
rf_cmoZ         =   input.int  (defval=54      , minval=1      , title='CMO Z Period'                                      , group='Range Filter'  )

RangeFilterSettings rangeFilterSettings = 
 RangeFilterSettings.new(
   input      (defval=close, title='Source'),
   rf_period,
   rf_smoothing,
   rf_cmoZ,
   input.float(defval = 6    , minval = 0.1    , title = 'Def RF Multiplier'  , tooltip = defMultiTooltip   , group = 'Range Filter'                     ),
   input.float(defval = 0.69 , minval = -10.1  , title = 'Bull RF Multi'      , tooltip = rngMulti1Tooltip  , group = 'Range Filter', inline = 'cmoBull' ),
   input.float(defval = 1.0  , minval = -5     , title = 'Bull CMO Z Ratio'                                 , group = 'Range Filter', inline = 'cmoBull' ),
   input.float(defval = 3    , minval = -10.1  , title = 'Neut RF Multi'      , tooltip = rngMulti2Tooltip  , group = 'Range Filter', inline = 'cmoNeut' ),
   input.float(defval = 0.5  , minval = -5     , title = 'Neut CMO Z Ratio'                                 , group = 'Range Filter', inline = 'cmoNeut' ),
   input.float(defval = 5    , minval = -10.1  , title = 'Bear CMO Multi'     , tooltip = rngMulti3Tooltip  , group = 'Range Filter', inline = 'cmoBear' ),
   input.float(defval = 0    , minval = -5     , title = 'Bear CMO Z Ratio'                                 , group = 'Range Filter', inline = 'cmoBear' ) 
 )

type AdxSettings
    bool    useAdxFilt
    int     adxLen
    int     adxTh
    int     upperLimit
    int     lowerLimit
    bool    isDynamicLimits
    int     minMaxPeriod
    color   limitsColor
    color   defaultColor

adx_len =   input.int  (defval = 12   , minval=1     , title='Filter Length'                           , group = 'ADX'  , inline = '1'),
adx_th  =   input.int  (defval = 20   , minval=-100  , title='Filter Threshold'                        , group = 'ADX'  , inline = '1'),

AdxSettings adxSettings = 
 AdxSettings.new(
   input.bool (defval = true                , title='Use ADX Filter?'    , tooltip = baseADX           , group = 'ADX'),
   adx_len,
   adx_th,
   input.int  (defval = 60   , minval=50    , title='Upper Limit'        , tooltip = upperLimitDescrp  , group = 'ADX' , inline = '2'),
   input.int  (defval = 15   , minval=1     , title='Lower Limit'        , tooltip = lowerLimitDescrp  , group = 'ADX' , inline = '2'),
   input.bool (defval = true                , title='Use Dyn Limits?'    , tooltip = visualTooltip     , group = 'ADX'),
   input.int  (defval = 84   , minval=2     , title='cmoLen Period'      , tooltip = minMaxPeriodDesc  , group = 'ADX'),
   input.color(defval = COLOR_GRAY          , title='Limits Color'       , tooltip = visualTooltip     , group = 'ADX'),
   input.color(defval = COLOR_BLUE          , title='Default Color'      , tooltip = visualTooltip     , group = 'ADX')
 )

type DisplaySettings
    bool  isEnableTrend
    color bullColor
    color bearColor
    bool  isEnableLimits
    color supplyColor
    color demandColor
    bool  isEnableSlope
    float adxSlopeThres
    float adxSlopeDisp
    color upSlopeColor
    color downSlopeColor
    color neutSlopeColor
    int   slopeLineWidth

rf_slope_width =    input.int  (defval = 1    , minval=1    , maxval=5         , title='Slope Dot Width'        , group = 'Slope Display')

DisplaySettings displaySettings = 
 DisplaySettings.new(
   input.bool (defval = true               , title='Enable Trend Display'      , group = 'Trend Display'                              ),
   input.color(defval = COLOR_TREND_BULL   , title='Bull Trend'                , group = 'Trend Display'        , inline = '21'       ),
   input.color(defval = COLOR_TREND_BEAR   , title='Bear Trend'                , group = 'Trend Display'        , inline = '21'       ),
   input.bool (defval = true               , title='Enable Extremes Display'   , group = 'Supply/Demand Display'                      ),
   input.color(defval = COLOR_SUPPLY       , title='Supply Zone'               , group = 'Supply/Demand Display', inline = '31'       ),
   input.color(defval = COLOR_DEMAND       , title='Demand Zone'               , group = 'Supply/Demand Display', inline = '31'       ),
   input.bool (defval = true               , title='Enable Slope Display'      , group = 'Slope Display'                              ),
   input.float(defval = 15   , minval=0.01 , title='Slope Threshold'           , tooltip = adxSlopeThresDesc , group = 'Slope Display'),
   input.float(defval = 5    , minval=0.01 , title='Slope Dots Displacement'   , tooltip = adxSlopeDispDescrp, group = 'Slope Display'),
   input.color(defval = COLOR_SLOPE_UP     , title='Up Slope'                  , group = 'Slope Display'        , inline = '41'       ),
   input.color(defval = COLOR_SLOPE_DOWN   , title='Down Slope'                , group = 'Slope Display'        , inline = '41'       ),
   input.color(defval = COLOR_SLOPE_NEUTRAL, title='Neutral Slope'             , group = 'Slope Display'        , inline = '41'       ),
   rf_slope_width
 )

tf1 = input.timeframe("15",  "MTF Timeframe #1 (Fastest)", group="Multi-Timeframe")
tf2 = input.timeframe("30", "MTF Timeframe #2", group="Multi-Timeframe")
tf3 = input.timeframe("60", "MTF Timeframe #3", group="Multi-Timeframe")
tf4 = input.timeframe("240",  "MTF Timeframe #4 (Slowest)", group="Multi-Timeframe")

type MTFSignalData
    float   adxValue
    float   adxSlope
    float   adxAccel
    float   rfValue
    float   rfSlope
    float   rfAccel
    float   cmoZ
    float   volumeWeight
    bool    rfBullish
    bool    adxStrong
    int     timeframeWeight

type ConfluenceMatrix
    float   adxAlignment
    float   rfMomentum
    float   cmoRegime
    float   volumeConfirmation
    float   slopeConvergence
    float   accelerationDivergence

type SignalOutput
    bool    buySignal
    bool    sellSignal
    int     confidence
    float   entryPrice
    float   stopLoss
    float   takeProfit
    string  regime
    float   positionSize

signal_group = "Signal Generation"
enable_signals      = input.bool(true, "Enable Signal Generation", group=signal_group)
confidence_threshold = input.int(75, "Min Confidence Level", minval=50, maxval=95, group=signal_group, tooltip="Minimum confidence for signal generation")
regime_sensitivity  = input.float(0.6, "Regime Detection Sensitivity", minval=0.1, maxval=1.0, step=0.1, group=signal_group)
mtf_weight_decay    = input.float(0.85, "MTF Weight Decay Factor", minval=0.5, maxval=0.95, step=0.05, group=signal_group, tooltip="How much to decay older timeframe signals")

risk_group = "Risk Management"
stop_loss_atr_mult  = input.float(2.2, "Stop Loss ATR Multiplier", minval=1.0, maxval=5.0, step=0.1, group=risk_group)
take_profit_ratio   = input.float(1.8, "Take Profit Ratio", minval=1.0, maxval=3.0, step=0.1, group=risk_group)
max_position_size   = input.float(100.0, "Max Position Size %", minval=10.0, maxval=100.0, step=5.0, group=risk_group)
dynamic_sizing      = input.bool(true, "Dynamic Position Sizing", group=risk_group, tooltip="Adjust position size based on signal confidence")

alert_group = "Alert Settings"
enable_alerts       = input.bool(true, "Enable Alerts", group=alert_group)
webhook_format      = input.bool(false, "Webhook JSON Format", group=alert_group, tooltip="Format alerts for trading bot integration")
alert_frequency     = input.string("Once Per Bar", "Alert Frequency", options=["Once Per Bar", "Once Per Bar Close"], group=alert_group)

calc_adx(length) =>
    ta.dmi(length, length)

calc_adx_limits(adxValue, useDynamic, upperPct, lowerPct, period) =>
    adxHigh = ta.highest(adxValue, period)
    adxLow = ta.lowest(adxValue, period)
    adxRange = adxHigh - adxLow
    upperLimit = useDynamic ? adxLow + upperPct * adxRange / 100 : upperPct
    lowerLimit = useDynamic ? adxLow + lowerPct * adxRange / 100 : lowerPct
    centerLine = (upperLimit + lowerLimit) / 2
    limitColor = adxValue > upperLimit ? color.green : adxValue < lowerLimit ? color.red : color.gray
    [upperLimit, lowerLimit, centerLine, limitColor]

calc_slope(feature, threshold, disp, upColor, downColor, neutColor) =>
    slope = math.atan((feature - feature[1]) / 2.0) * 180.0 / math.pi
    slopeColor = slope > threshold ? downColor : slope < -threshold ? upColor : neutColor
    [feature + disp, slopeColor]

mtf_analysis(_src) =>
    cmoRaw = ta.cmo(_src, rf_period)
    cmoZ = (cmoRaw - ta.sma(cmoRaw, rf_cmoZ)) / ta.stdev(cmoRaw, rf_cmoZ)
    mult = cmoZ > rangeFilterSettings.cmoBullZ ? rangeFilterSettings.rangeMulti1 : cmoZ > rangeFilterSettings.cmoNeutZ ? rangeFilterSettings.rangeMulti2 : cmoZ < rangeFilterSettings.cmoBearZ ? rangeFilterSettings.rangeMulti3 : rangeFilterSettings.rangeMult0
    avrng = ta.ema(math.abs(_src - _src[1]), rf_smoothing)
    smrng = ta.ema(avrng, rf_smoothing * 2 - 1) * mult
    
    var float rfVal = na
    rfVal := na(rfVal) ? _src : (_src > rfVal ? math.max(rfVal, _src - smrng) : math.min(rfVal, _src + smrng))
    rfMomentum = rfVal - nz(rfVal[1], rfVal)
    rfAcceleration = rfMomentum - nz(rfVal[1] - rfVal[2], 0)

    [adxValue, _, _] = ta.dmi(adx_len, adx_len)
    adxMomentum = adxValue - nz(adxValue[1], adxValue)
    adxAcceleration = adxMomentum - nz(adxValue[1] - adxValue[2], 0)

    rfBullish = rfMomentum > 0
    adxStrong = adxSettings.useAdxFilt ? adxValue > adxSettings.adxTh : true
    volumeWeight = volume / ta.sma(volume, 20)
    multColor = mult == rangeFilterSettings.rangeMulti1 ? COLOR_CMO_M1 : mult == rangeFilterSettings.rangeMulti2 ? COLOR_CMO_M2 : mult == rangeFilterSettings.rangeMulti3 ? COLOR_CMO_M3 : COLOR_CM0_M0
    [smrng, cmoZ, mult, multColor, rfVal, rfBullish ? 1 : 0, rfBullish ? 0 : 1, adxStrong, rfMomentum, rfAcceleration, adxMomentum, adxAcceleration, volumeWeight, adxValue]

timeframes = array.from(tf1, tf2, tf3, tf4)
var mtfData = array.new<array<float>>(4)

for i = 0 to 3
    tf = array.get(timeframes, i)
    data = request.security(syminfo.tickerid, tf, mtf_analysis(src))
    array.set(mtfData, i, data)

[tf1Smrg, tf1_cmo, tf1_multi, tf1_mColor, rf1Filt, tf1_rfUp, tf1_rfDn, tf1_adxUp, tf1_rfSlope, tf1_rfAccel, tf1_adxSlope, tf1_adxAccel, tf1_volWeight, tf1_adxVal] = array.get(mtfData, 0)
[tf2Smrg, tf2_cmo, tf2_multi, tf2_mColor, rf2Filt, tf2_rfUp, tf2_rfDn, tf2_adxUp, tf2_rfSlope, tf2_rfAccel, tf2_adxSlope, tf2_adxAccel, tf2_volWeight, tf2_adxVal] = array.get(mtfData, 1)
[tf3Smrg, tf3_cmo, tf3_multi, tf3_mColor, rf3Filt, tf3_rfUp, tf3_rfDn, tf3_adxUp, tf3_rfSlope, tf3_rfAccel, tf3_adxSlope, tf3_adxAccel, tf3_volWeight, tf3_adxVal] = array.get(mtfData, 2)
[tf4Smrg, tf4_cmo, tf4_multi, tf4_mColor, rf4Filt, tf4_rfUp, tf4_rfDn, tf4_adxUp, tf4_rfSlope, tf4_rfAccel, tf4_adxSlope, tf4_adxAccel, tf4_volWeight, tf4_adxVal] = array.get(mtfData, 3)

[adx, plusDI, minusDI]                                          = calc_adx(adx_len)
[adxSlope, adxSlopeColor]                                       = calc_slope(adx, displaySettings.adxSlopeThres, displaySettings.adxSlopeDisp, displaySettings.upSlopeColor, displaySettings.downSlopeColor, displaySettings.neutSlopeColor)
[adxSupplyLine, adxDemandLine, adxCenterLine, adxColor]         = calc_adx_limits(adx, adxSettings.isDynamicLimits, adxSettings.upperLimit, adxSettings.lowerLimit, adxSettings.minMaxPeriod)

// Most efficient confluence scoring - direct calculations
calc_confluence_score() =>
    adxAlignment = (tf1_adxVal * 1.0 + tf2_adxVal * 0.85 + tf3_adxVal * 0.7 + tf4_adxVal * 0.55) / 3.1
    rfMomentum = (tf1_rfSlope * 1.0 + tf2_rfSlope * 0.85 + tf3_rfSlope * 0.7 + tf4_rfSlope * 0.55) / 3.1
    cmoRegime = tf1_cmo > 1.0 ? "trending" : tf1_cmo > -1.0 ? "transitional" : "ranging"
    volConfirmation = (tf1_volWeight + tf2_volWeight + tf3_volWeight + tf4_volWeight) * 0.25
    slopeConvergence = math.abs(tf1_rfSlope - tf2_rfSlope) + math.abs(tf2_rfSlope - tf3_rfSlope) + math.abs(tf3_rfSlope - tf4_rfSlope)
    accelDivergence = math.abs(tf1_rfAccel - tf2_rfAccel) + math.abs(tf2_rfAccel - tf3_rfAccel) + math.abs(tf3_rfAccel - tf4_rfAccel)
    [adxAlignment, rfMomentum, cmoRegime, volConfirmation, slopeConvergence, accelDivergence]

generate_signals() =>
    [adxAlign, rfMom, regime, volConf, slopeConv, accelDiv] = calc_confluence_score()
    baseThreshold = regime == "trending" ? 0.6 : regime == "transitional" ? 0.8 : 1.0
    rfAgreement = (tf1_rfUp > 0 ? 1 : 0) + (tf2_rfUp > 0 ? 1 : 0) + (tf3_rfUp > 0 ? 1 : 0) + (tf4_rfUp > 0 ? 1 : 0)
    adxAgreement = (tf1_adxUp ? 1 : 0) + (tf2_adxUp ? 1 : 0) + (tf3_adxUp ? 1 : 0) + (tf4_adxUp ? 1 : 0)
    signalStrength = (rfAgreement + adxAgreement) * 12.5
    momentumFactor = (tf1_rfAccel + tf1_adxAccel) * 0.5
    buySignal = rfAgreement >= 3 and adxAgreement >= 2 and rfMom > baseThreshold and momentumFactor > 0
    sellSignal = rfAgreement <= 1 and adxAgreement <= 2 and rfMom < -baseThreshold and momentumFactor < 0
    confidence = math.round(signalStrength * volConf * (1 - slopeConv * 0.1) * (1 - accelDiv * 0.1))
    confidence := math.max(0, math.min(100, confidence))
    [buySignal, sellSignal, confidence, regime, signalStrength]

[buySignal, sellSignal, signalConfidence, marketRegime, signalStrength] = generate_signals()
positionSize = dynamic_sizing ? (signalConfidence / 100) * max_position_size : max_position_size
atr = ta.atr(14)
stopLoss = buySignal ? close - (atr * stop_loss_atr_mult) : sellSignal ? close + (atr * stop_loss_atr_mult) : na
takeProfit = buySignal ? close + (atr * stop_loss_atr_mult * take_profit_ratio) : sellSignal ? close - (atr * stop_loss_atr_mult * take_profit_ratio) : na

if enable_signals and signalConfidence >= confidence_threshold
    if buySignal and not strategy.position_size
        strategy.entry("Long", strategy.long, qty=positionSize/100, comment="MTF Buy " + str.tostring(signalConfidence))
        strategy.exit("Long Exit", "Long", stop=stopLoss, limit=takeProfit)
    if sellSignal and not strategy.position_size
        strategy.entry("Short", strategy.short, qty=positionSize/100, comment="MTF Sell " + str.tostring(signalConfidence))
        strategy.exit("Short Exit", "Short", stop=stopLoss, limit=takeProfit)

if enable_alerts and signalConfidence >= confidence_threshold
    if buySignal and not buySignal[1]
        alert_msg = webhook_format ?
         '{"action":"buy","symbol":"ETHUSDT","confidence":' + str.tostring(signalConfidence) + ',"regime":"' + marketRegime + '","price":' + str.tostring(close) + '}' :
         "BUY SIGNAL: ETH/USDT | Confidence: " + str.tostring(signalConfidence) + "% | Regime: " + marketRegime + " | Price: " + str.tostring(close)
        alert(alert_msg, alert_frequency == "Once Per Bar" ? alert.freq_once_per_bar : alert.freq_once_per_bar_close)
    if sellSignal and not sellSignal[1]
        alert_msg = webhook_format ?
         '{"action":"sell","symbol":"ETHUSDT","confidence":' + str.tostring(signalConfidence) + ',"regime":"' + marketRegime + '","price":' + str.tostring(close) + '}' :
         "SELL SIGNAL: ETH/USDT | Confidence: " + str.tostring(signalConfidence) + "% | Regime: " + marketRegime + " | Price: " + str.tostring(close)
        alert(alert_msg, alert_frequency == "Once Per Bar" ? alert.freq_once_per_bar : alert.freq_once_per_bar_close)

plot(-10, title="RF1 ", style=plot.style_circles, linewidth=1, color=tf1_rfUp  ? color.green : color.red)
plot(-13, title="ADX1", style=plot.style_circles, linewidth=1, color=tf1_adxUp ? color.green : color.red)
plot(-20, title="RF2",  style=plot.style_circles, linewidth=1, color=tf2_rfUp  ? color.green : color.red)
plot(-23, title="ADX2", style=plot.style_circles, linewidth=1, color=tf2_adxUp ? color.green : color.red)
plot(-30, title="RF3",  style=plot.style_circles, linewidth=1, color=tf3_rfUp  ? color.green : color.red)
plot(-33, title="ADX3", style=plot.style_circles, linewidth=1, color=tf3_adxUp ? color.green : color.red)
plot(-40, title="RF4",  style=plot.style_circles, linewidth=1, color=tf4_rfUp  ? color.green : color.red)
plot(-43, title="ADX4", style=plot.style_circles, linewidth=1, color=tf4_adxUp ? color.green : color.red)
plot(signalConfidence, title="Signal Confidence", color=color.new(color.white, 0), linewidth=2)
plot(signalStrength, title="Signal Strength", color=color.new(color.yellow, 0), linewidth=1)
plot(buySignal and signalConfidence >= confidence_threshold ? 90 : na, title="Buy Signal", color=color.new(color.lime, 0), style=plot.style_triangleup, linewidth=4)
plot(sellSignal and signalConfidence >= confidence_threshold ? 10 : na, title="Sell Signal", color=color.new(color.red, 0), style=plot.style_triangledown, linewidth=4)
plot(tf1_cmo, title="CMO Z-Score", color=tf1_mColor, linewidth=2, style=plot.style_columns)
plot(adxSupplyLine, title="ADX Upper Limit", color=adxSettings.limitsColor)
plot(adxDemandLine, title="ADX Lower Limit", color=adxSettings.limitsColor)
plot(adxCenterLine, title="ADX Center Line", color=adxSettings.limitsColor)
plot(adx, title="ADX", color=adxColor, linewidth=2)
plot(displaySettings.isEnableSlope ? adxSlope : na, title="ADX Slope", color=adxSlopeColor, style=plot.style_circles, linewidth=rf_slope_width)
filtPlot = plot(rf1Filt, color=tf1_rfUp > 0 ? color.rgb(0, 230, 118) : tf1_rfDn > 0 ? color.rgb(255, 82, 82) : color.rgb(255, 152, 0), linewidth=1, title='Range Filter TF1', force_overlay=true)
hBand = rf1Filt + tf1Smrg
lBand = rf1Filt - tf1Smrg
plot(hBand, color=color.new(color.aqua, 100), title='High Target', force_overlay=true)
plot(lBand, color=color.new(color.fuchsia, 100), title='Low Target', force_overlay=true)
fill(plot(hBand, force_overlay=true, display=display.data_window), plot(rf1Filt, force_overlay=true, display=display.data_window), color=color.new(color.aqua, 90), title='High Target Range')
fill(plot(lBand, force_overlay=true, display=display.data_window), plot(rf1Filt, force_overlay=true, display=display.data_window), color=color.new(color.fuchsia, 90), title='Low Target Range')
plot(rf2Filt, color=tf2_rfUp > 0 ? color.rgb(0, 230, 118, 20) : tf2_rfDn > 0 ? color.rgb(255, 82, 82, 20) : color.rgb(255, 152, 0, 20), linewidth=2, title='Range Filter TF2', force_overlay=true)
plot(rf3Filt, color=tf3_rfUp > 0 ? color.rgb(0, 230, 118, 40) : tf3_rfDn > 0 ? color.rgb(255, 82, 82, 40) : color.rgb(255, 152, 0, 40), linewidth=3, title='Range Filter TF3', force_overlay=true)
plot(rf4Filt, color=tf4_rfUp > 0 ? color.rgb(0, 230, 118, 60) : tf4_rfDn > 0 ? color.rgb(255, 82, 82, 60) : color.rgb(255, 152, 0, 60), linewidth=4, title='Range Filter TF4', force_overlay=true)