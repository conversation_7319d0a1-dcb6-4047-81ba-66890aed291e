// © cryptokairo
//@version=5
strategy("S&K Refactored_5-18-25_MTF CMO Screener", overlay=false,
  commission_type = strategy.commission.percent, commission_value = 0.00,
  default_qty_type = strategy.percent_of_equity, default_qty_value = 100,
  initial_capital = 2000, slippage = 0, calc_on_every_tick = true, process_orders_on_close = false
  )
COLOR_GRAY           = color.rgb(87, 89, 94)
COLOR_BLUE           = color.rgb(64, 118, 179)
COLOR_TREND_BULL     = color.rgb(16 , 228, 217)
COLOR_TREND_BEAR     = color.rgb(201, 206, 63)
COLOR_SUPPLY         = color.rgb(170, 0  , 10)
COLOR_DEMAND         = color.rgb(0  , 190, 120)
COLOR_SLOPE_UP       = color.rgb(147, 212, 68)
COLOR_SLOPE_DOWN     = color.rgb(206, 60 , 140)
COLOR_SLOPE_NEUTRAL  = color.rgb(165, 165, 165)
COLOR_CMO_M1         = color.rgb(76, 175, 79, 50)
COLOR_CMO_M2         = color.rgb(255, 235, 59, 50)
COLOR_CMO_M3         = color.rgb(255, 152, 0, 50)
COLOR_CM0_M0         = color.rgb(255, 82, 82, 50)
// ===┌────────────────────────────────────┐
//      Initialize Variables
// ===└────────────────────────────────────┘
// State Management
var int condIni = 0
// ===┌────────────────────────────────────┐
//      Tooltip Constants
// ===└────────────────────────────────────┘
var const string visualTooltip      = "Visual Control Only"
var const string baseADX            = "Base ADX Condition, is true if ADX > Threshold"
var const string dynSLTooltip       = "ADX Long Threshold used for Tiered Stop Loss"
var const string adxLongPlotTooltip = "Toggle ADX Long used to determine if the Condition 'ADX Long > ADX Long Threshold' is true"
var const string upperLimitDescrp   = "Upper limit for ADX indicator"
var const string lowerLimitDescrp   = "Lower limit for ADX indicator"
var const string minMaxPeriodDesc   = "cmoLen period for ADX slope Tiered limits calculation"
var const string adxSlopeThresDesc  = "Threshold for ADX slope detection"
var const string adxSlopeDispDescrp = "Displacement for slope dots"
var const string setPNLTooltip      = "Profit level that must be reached before tracking retracement"
var const string deltaPNLTooltip    = "Amount of retracement from max profit that will trigger exit"
var const string rngcmoLenTooltip   = "Range cmoLen in period for the range filter multiplier calculation"
var const string rngSmthLenTooltip  = "Range smoothing length"
var const string defMultiTooltip    = "Default multiplier used for the CMO to determine current multiplier for the Smoothed Range Calculation"
var const string rngMulti1Tooltip   = "RF Multi 1 to be used if CMO < cmoBullZ to set the current multiplier for the Smoothed Range Calculation"
var const string rngMulti2Tooltip   = "RF Multi 2 to be used if CMO < cmoNeutZ to set the current multiplier for the Smoothed Range Calculation"
var const string rngMulti3Tooltip   = "RF Multi 3 to be used if CMO < cmoNeutZ to set the current multiplier for the Smoothed Range Calculation"
var const string cmoBullZTooltip    = rngMulti1Tooltip
var const string cmoNeutZTooltip    = rngMulti2Tooltip
var const string cmoBearZTooltip    = rngMulti3Tooltip

src = input(defval=close, title='Default Source')
// ===┌────────────────────────────────────┐
//      Range Filter Settings & Config
// ===└────────────────────────────────────┘
type RangeFilterSettings
    float   rfSrc
    int     rfPeriod
    int     rfSmthLen
    int     cmoZPeriod
    float   rangeMult0
    float   rangeMulti1
    float   cmoBullZ
    float   rangeMulti2
    float   cmoNeutZ
    float   rangeMulti3
    float   cmoBearZ

rf_period       =   input.int  (defval=34       , minval=1      , title='Range Period'       , tooltip = rngcmoLenTooltip   , group='Range Filter'  )
rf_smoothing    =   input.int  (defval=78       , minval=1      , title='Smoothing Length'   , tooltip = rngSmthLenTooltip  , group='Range Filter'  )
rf_cmoZ         =   input.int  (defval=54      , minval=1      , title='CMO Z Period'                                      , group='Range Filter'  )

RangeFilterSettings rangeFilterSettings = 
 RangeFilterSettings.new(
   input      (defval=close, title='Source'),
   rf_period,
   rf_smoothing,
   rf_cmoZ,
   input.float(defval = 6    , minval = 0.1    , title = 'Def RF Multiplier'  , tooltip = defMultiTooltip   , group = 'Range Filter'                     ),
   input.float(defval = 0.69 , minval = -10.1  , title = 'Bull RF Multi'      , tooltip = rngMulti1Tooltip  , group = 'Range Filter', inline = 'cmoBull' ),
   input.float(defval = 1.0  , minval = -5     , title = 'Bull CMO Z Ratio'                                 , group = 'Range Filter', inline = 'cmoBull' ),
   input.float(defval = 3    , minval = -10.1  , title = 'Neut RF Multi'      , tooltip = rngMulti2Tooltip  , group = 'Range Filter', inline = 'cmoNeut' ),
   input.float(defval = 0.5  , minval = -5     , title = 'Neut CMO Z Ratio'                                 , group = 'Range Filter', inline = 'cmoNeut' ),
   input.float(defval = 5    , minval = -10.1  , title = 'Bear CMO Multi'     , tooltip = rngMulti3Tooltip  , group = 'Range Filter', inline = 'cmoBear' ),
   input.float(defval = 0    , minval = -5     , title = 'Bear CMO Z Ratio'                                 , group = 'Range Filter', inline = 'cmoBear' ) 
 )
// ===┌────────────────────────────────────┐
//      ADX Settings & Config
// ===└────────────────────────────────────┘
type AdxSettings
    bool    useAdxFilt
    int     adxLen
    int     adxTh
    int     upperLimit
    int     lowerLimit
    bool    isDynamicLimits
    int     minMaxPeriod
    color   limitsColor
    color   defaultColor

adx_len =   input.int  (defval = 12   , minval=1     , title='Filter Length'                           , group = 'ADX'  , inline = '1'),
adx_th  =   input.int  (defval = 20   , minval=-100  , title='Filter Threshold'                        , group = 'ADX'  , inline = '1'),

AdxSettings adxSettings = 
 AdxSettings.new(
   input.bool (defval = true                , title='Use ADX Filter?'    , tooltip = baseADX           , group = 'ADX'),
   adx_len,
   adx_th,
   input.int  (defval = 60   , minval=50    , title='Upper Limit'        , tooltip = upperLimitDescrp  , group = 'ADX' , inline = '2'),
   input.int  (defval = 15   , minval=1     , title='Lower Limit'        , tooltip = lowerLimitDescrp  , group = 'ADX' , inline = '2'),
   input.bool (defval = true                , title='Use Dyn Limits?'    , tooltip = visualTooltip     , group = 'ADX'),
   input.int  (defval = 84   , minval=2     , title='cmoLen Period'      , tooltip = minMaxPeriodDesc  , group = 'ADX'),
   input.color(defval = COLOR_GRAY          , title='Limits Color'       , tooltip = visualTooltip     , group = 'ADX'),
   input.color(defval = COLOR_BLUE          , title='Default Color'      , tooltip = visualTooltip     , group = 'ADX')
 )

type DisplaySettings
    bool  isEnableTrend
    color bullColor
    color bearColor
    bool  isEnableLimits
    color supplyColor
    color demandColor
    bool  isEnableSlope
    float adxSlopeThres
    float adxSlopeDisp
    color upSlopeColor
    color downSlopeColor
    color neutSlopeColor
    int   slopeLineWidth

rf_slope_width =    input.int  (defval = 1    , minval=1    , maxval=5         , title='Slope Dot Width'        , group = 'Slope Display')

DisplaySettings displaySettings = 
 DisplaySettings.new(
   input.bool (defval = true               , title='Enable Trend Display'      , group = 'Trend Display'                              ),
   input.color(defval = COLOR_TREND_BULL   , title='Bull Trend'                , group = 'Trend Display'        , inline = '21'       ),
   input.color(defval = COLOR_TREND_BEAR   , title='Bear Trend'                , group = 'Trend Display'        , inline = '21'       ),
   input.bool (defval = true               , title='Enable Extremes Display'   , group = 'Supply/Demand Display'                      ),
   input.color(defval = COLOR_SUPPLY       , title='Supply Zone'               , group = 'Supply/Demand Display', inline = '31'       ),
   input.color(defval = COLOR_DEMAND       , title='Demand Zone'               , group = 'Supply/Demand Display', inline = '31'       ),
   input.bool (defval = true               , title='Enable Slope Display'      , group = 'Slope Display'                              ),
   input.float(defval = 15   , minval=0.01 , title='Slope Threshold'           , tooltip = adxSlopeThresDesc , group = 'Slope Display'),
   input.float(defval = 5    , minval=0.01 , title='Slope Dots Displacement'   , tooltip = adxSlopeDispDescrp, group = 'Slope Display'),
   input.color(defval = COLOR_SLOPE_UP     , title='Up Slope'                  , group = 'Slope Display'        , inline = '41'       ),
   input.color(defval = COLOR_SLOPE_DOWN   , title='Down Slope'                , group = 'Slope Display'        , inline = '41'       ),
   input.color(defval = COLOR_SLOPE_NEUTRAL, title='Neutral Slope'             , group = 'Slope Display'        , inline = '41'       ),
   rf_slope_width
 )

tf1 = input.timeframe("15",  "MTF Timeframe #1")
tf2 = input.timeframe("30", "MTF Timeframe #2")
tf3 = input.timeframe("45", "MTF Timeframe #3")
tf4 = input.timeframe("60",  "MTF Timeframe #4")
// ===┌────────────────────────────────────┐
//      Calculations
// ===└────────────────────────────────────┘
calcADX(len) =>
    tr          = math.max(math.max(high - low, math.abs(high - nz(close[1]))), math.abs(low - nz(close[1])))
    upMove      = high - nz(high[1])
    downMove    = nz(low[1]) - low
    plusDM      = upMove   > downMove and upMove > 0 ? upMove   : 0
    minusDM     = downMove > upMove and downMove > 0 ? downMove : 0
    trSum       = ta.rma(tr, len)
    plusDMSum   = ta.rma(plusDM, len)
    minusDMSum  = ta.rma(minusDM, len)
    plusDI      = 100 * plusDMSum / trSum
    minusDI     = 100 * minusDMSum / trSum
    dx          = 100 * math.abs(plusDI - minusDI) / (plusDI + minusDI)
    adx         = ta.rma(dx, len)
    [adx, plusDI, minusDI]

calcADXLimits( _adx, _useDynamic, _upperPct, _lowerPct, _period) =>
    maxADX      = ta.highest(_adx, _period)
    minADX      = ta.lowest(_adx, _period)
    adxRange    = maxADX - minADX
    upper       = _useDynamic ? minADX + _upperPct * adxRange / 100 : _upperPct
    lower       = _useDynamic ? minADX + _lowerPct * adxRange / 100 : _lowerPct
    center      = (upper + lower) / 2
    color       = _adx > upper ? color.green : _adx < lower ? color.red : color.gray
    [upper, lower, center, color]

smoothRange(src, period, zPeriod, cmoZ1, cmoZ2, cmoZ3, mult0, mult1, mult2, mult3, lookback) =>
    wper        = period * 2 - 1
    avrng       = ta.ema(math.abs(src - src[1]), period)
    cmoRaw      = ta.cmo(close, lookback)
    cmoMean     = ta.sma(cmoRaw, zPeriod)
    cmoStd      = ta.stdev(cmoRaw, zPeriod)
    cmoZ        = (cmoRaw - cmoMean) / cmoStd
    mult        = cmoZ > cmoZ1 ? mult1 : cmoZ > cmoZ2 ? mult2 : cmoZ < cmoZ3 ? mult3 : mult0
    multColor   = mult == mult1 ? COLOR_CMO_M1 : mult == mult2 ? COLOR_CMO_M2 : mult == mult3 ? COLOR_CMO_M3 : COLOR_CM0_M0
    smooth      = ta.ema(avrng, wper) * mult
    [smooth, cmoZ, mult, multColor]

rangeFilter(src, smrng, filtPrev) =>
    filtNew     = src > nz(filtPrev) ? (src - smrng < nz(filtPrev) ? nz(filtPrev) : src - smrng) : src + smrng > nz(filtPrev) ? nz(filtPrev) : src + smrng
    filtNew

calcSlope(feature, threshold, disp, upColor, downColor, neutColor) =>
    slope       = math.atan((feature - feature[1]) / 2.0) * 180.0 / math.pi
    slopeColor  = neutColor
    slopePlot   = feature + disp
    if slope > threshold
        slopeColor := downColor
    else if slope < -threshold
        slopeColor := upColor
    [slopePlot, slopeColor]

f_mtflogic(_src) =>
    float rfVal = na
    float rfUp = 0.0
    float rfDn = 0.0
    [_smrng, _cmo, _cMulti, _mColor] = smoothRange(
       _src, rf_smoothing, rf_cmoZ,
       rangeFilterSettings.cmoBullZ,
       rangeFilterSettings.cmoNeutZ,
       rangeFilterSettings.cmoBearZ,
       rangeFilterSettings.rangeMult0,
       rangeFilterSettings.rangeMulti1,
       rangeFilterSettings.rangeMulti2,
       rangeFilterSettings.rangeMulti3,
       rf_period
     )
    rfVal           := rangeFilter(_src, _smrng, nz(rfVal[1]))
    rfUp            := rfVal > rfVal[1] ? nz(rfUp[1]) + 1 : rfVal < rfVal[1] ? 0 : nz(rfUp[1])
    rfDn            := rfVal < rfVal[1] ? nz(rfDn[1]) + 1 : rfVal > rfVal[1] ? 0 : nz(rfDn[1])
    [adxVal, _, _]  = calcADX(adx_len)
    adxOk           = adxSettings.useAdxFilt ? (adxVal > adxSettings.adxTh) : true
    [_smrng, _cmo, _cMulti, _mColor, rfVal, rfUp, rfDn, adxOk]

[tf1Smrg, tf1_cmo, tf1_multi, tf1_mColor, rf1Filt, tf1_rfUp, tf1_rfDn, tf1_adxUp]   = request.security(syminfo.tickerid, tf1, f_mtflogic(src))
[tf2Smrg, tf2_cmo, tf2_multi, tf2_mColor, rf2Filt, tf2_rfUp, tf2_rfDn, tf2_adxUp]   = request.security(syminfo.tickerid, tf2, f_mtflogic(src))
[tf3Smrg, tf3_cmo, tf3_multi, tf3_mColor, rf3Filt, tf3_rfUp, tf3_rfDn, tf3_adxUp]   = request.security(syminfo.tickerid, tf3, f_mtflogic(src))
[tf4Smrg, tf4_cmo, tf4_multi, tf4_mColor, rf4Filt, tf4_rfUp, tf4_rfDn, tf4_adxUp]   = request.security(syminfo.tickerid, tf4, f_mtflogic(src))
[adx, plusDI, minusDI]                                          = calcADX(adx_len)
[adxSlope, adxSlopeColor]                                       = calcSlope(adx, displaySettings.adxSlopeThres, displaySettings.adxSlopeDisp, displaySettings.upSlopeColor, displaySettings.downSlopeColor, displaySettings.neutSlopeColor)
[adxSupplyLine, adxDemandLine, adxCenterLine, adxColor]         = calcADXLimits(adx, adxSettings.isDynamicLimits, adxSettings.upperLimit, adxSettings.lowerLimit, adxSettings.minMaxPeriod)

plot(-10, title="RF1 ", style=plot.style_circles, linewidth=1, color=tf1_rfUp  ? color.green : color.red)
plot(-13, title="ADX1", style=plot.style_circles, linewidth=1, color=tf1_adxUp ? color.green : color.red)
plot(-20, title="RF2",  style=plot.style_circles, linewidth=1, color=tf2_rfUp  ? color.green : color.red)
plot(-23, title="ADX2", style=plot.style_circles, linewidth=1, color=tf2_adxUp ? color.green : color.red)
plot(-30, title="RF3",  style=plot.style_circles, linewidth=1, color=tf3_rfUp  ? color.green : color.red)
plot(-33, title="ADX3", style=plot.style_circles, linewidth=1, color=tf3_adxUp ? color.green : color.red)
plot(-40, title="RF4",  style=plot.style_circles, linewidth=1, color=tf4_rfUp  ? color.green : color.red)
plot(-43, title="ADX4", style=plot.style_circles, linewidth=1, color=tf4_adxUp ? color.green : color.red)
plot(tf1_cmo, title="cmoZ", color=tf1_mColor, linewidth=2, style = plot.style_columns)
plot(adxSupplyLine, title="ADX Upper Limit", color=adxSettings.limitsColor)
plot(adxDemandLine, title="ADX Lower Limit", color=adxSettings.limitsColor)
plot(adxCenterLine, title="ADX Center Line", color=adxSettings.limitsColor)
plot(adx, title="ADX", color=adxColor, linewidth=2)
plot(displaySettings.isEnableSlope ? adxSlope : na, title="ADX Slope", color=adxSlopeColor, style=plot.style_circles, linewidth=rf_slope_width)
filtplot = plot(rf1Filt, color=tf1_rfUp > 0 ? color.rgb(0, 230, 118) : tf1_rfDn > 0 ? color.rgb(255, 82, 82) : color.rgb(255, 152, 0), linewidth=1, title='Range Filter TF1', force_overlay=true)
hband = rf1Filt + tf1Smrg
lband = rf1Filt - tf1Smrg
plot(hband, color=color.new(color.aqua, 100), title='High Target', force_overlay=true)
plot(lband, color=color.new(color.fuchsia, 100), title='Low Target', force_overlay=true)
fill(plot(hband,force_overlay = true,  display = display.data_window), plot(rf1Filt, force_overlay = true,  display = display.data_window), color=color.new(color.aqua, 90), title='High Target Range')
fill(plot(lband, force_overlay = true,  display = display.data_window), plot(rf1Filt, force_overlay = true,  display = display.data_window), color=color.new(color.fuchsia, 90), title='Low Target Range')
plot(rf2Filt, color=tf2_rfUp > 0 ? color.rgb(0, 230, 118, 20) : tf2_rfDn > 0 ? color.rgb(255, 82, 82, 20) : color.rgb(255, 152, 0, 20), linewidth=2, title='Range Filter TF2', force_overlay=true)
plot(rf3Filt, color=tf3_rfUp > 0 ? color.rgb(0, 230, 118, 40) : tf3_rfDn > 0 ? color.rgb(255, 82, 82, 40) : color.rgb(255, 152, 0, 40), linewidth=3, title='Range Filter TF3', force_overlay=true)
plot(rf4Filt, color=tf3_rfUp > 0 ? color.rgb(0, 230, 118, 60) : tf4_rfDn > 0 ? color.rgb(255, 82, 82, 60) : color.rgb(255, 152, 0, 60), linewidth=4, title='Range Filter TF4', force_overlay=true)