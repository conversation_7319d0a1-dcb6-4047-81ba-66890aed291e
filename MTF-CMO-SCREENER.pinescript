// © cryptokairo
//@version=5
strategy("S&K ETH/USDT 15m Advanced MTF Signal Generator", overlay=false,
  commission_type = strategy.commission.percent, commission_value = 0.00,
  default_qty_type = strategy.percent_of_equity, default_qty_value = 100,
  initial_capital = 2000, slippage = 0, calc_on_every_tick = true, process_orders_on_close = false
  )
COLOR_GRAY           = color.rgb(87, 89, 94)
COLOR_BLUE           = color.rgb(64, 118, 179)
COLOR_TREND_BULL     = color.rgb(16 , 228, 217)
COLOR_TREND_BEAR     = color.rgb(201, 206, 63)
COLOR_SUPPLY         = color.rgb(170, 0  , 10)
COLOR_DEMAND         = color.rgb(0  , 190, 120)
COLOR_SLOPE_UP       = color.rgb(147, 212, 68)
COLOR_SLOPE_DOWN     = color.rgb(206, 60 , 140)
COLOR_SLOPE_NEUTRAL  = color.rgb(165, 165, 165)
COLOR_CMO_M1         = color.rgb(76, 175, 79, 50)
COLOR_CMO_M2         = color.rgb(255, 235, 59, 50)
COLOR_CMO_M3         = color.rgb(255, 152, 0, 50)
COLOR_CM0_M0         = color.rgb(255, 82, 82, 50)

var int condIni = 0
var const string visualTooltip      = "Visual Control Only"
var const string baseADX            = "Base ADX Condition, is true if ADX > Threshold"
var const string dynSLTooltip       = "ADX Long Threshold used for Tiered Stop Loss"
var const string adxLongPlotTooltip = "Toggle ADX Long used to determine if the Condition 'ADX Long > ADX Long Threshold' is true"
var const string upperLimitDescrp   = "Upper limit for ADX indicator"
var const string lowerLimitDescrp   = "Lower limit for ADX indicator"
var const string minMaxPeriodDesc   = "cmoLen period for ADX slope Tiered limits calculation"
var const string adxSlopeThresDesc  = "Threshold for ADX slope detection"
var const string adxSlopeDispDescrp = "Displacement for slope dots"
var const string setPNLTooltip      = "Profit level that must be reached before tracking retracement"
var const string deltaPNLTooltip    = "Amount of retracement from max profit that will trigger exit"
var const string rngcmoLenTooltip   = "Range cmoLen in period for the range filter multiplier calculation"
var const string rngSmthLenTooltip  = "Range smoothing length"
var const string defMultiTooltip    = "Default multiplier used for the CMO to determine current multiplier for the Smoothed Range Calculation"
var const string rngMulti1Tooltip   = "RF Multi 1 to be used if CMO < cmoBullZ to set the current multiplier for the Smoothed Range Calculation"
var const string rngMulti2Tooltip   = "RF Multi 2 to be used if CMO < cmoNeutZ to set the current multiplier for the Smoothed Range Calculation"
var const string rngMulti3Tooltip   = "RF Multi 3 to be used if CMO < cmoNeutZ to set the current multiplier for the Smoothed Range Calculation"
var const string cmoBullZTooltip    = rngMulti1Tooltip
var const string cmoNeutZTooltip    = rngMulti2Tooltip
var const string cmoBearZTooltip    = rngMulti3Tooltip

src = input(defval=close, title='Default Source')

type RangeFilterSettings
    float   rfSrc
    int     rfPeriod
    int     rfSmthLen
    int     cmoZPeriod
    float   rangeMult0
    float   rangeMulti1
    float   cmoBullZ
    float   rangeMulti2
    float   cmoNeutZ
    float   rangeMulti3
    float   cmoBearZ

rf_period       =   input.int  (defval=34       , minval=1      , title='Range Period'       , tooltip = rngcmoLenTooltip   , group='Range Filter'  )
rf_smoothing    =   input.int  (defval=78       , minval=1      , title='Smoothing Length'   , tooltip = rngSmthLenTooltip  , group='Range Filter'  )
rf_cmoZ         =   input.int  (defval=54      , minval=1      , title='CMO Z Period'                                      , group='Range Filter'  )

RangeFilterSettings rangeFilterSettings = 
 RangeFilterSettings.new(
   input      (defval=close, title='Source'),
   rf_period,
   rf_smoothing,
   rf_cmoZ,
   input.float(defval = 6    , minval = 0.1    , title = 'Def RF Multiplier'  , tooltip = defMultiTooltip   , group = 'Range Filter'                     ),
   input.float(defval = 0.69 , minval = -10.1  , title = 'Bull RF Multi'      , tooltip = rngMulti1Tooltip  , group = 'Range Filter', inline = 'cmoBull' ),
   input.float(defval = 1.0  , minval = -5     , title = 'Bull CMO Z Ratio'                                 , group = 'Range Filter', inline = 'cmoBull' ),
   input.float(defval = 3    , minval = -10.1  , title = 'Neut RF Multi'      , tooltip = rngMulti2Tooltip  , group = 'Range Filter', inline = 'cmoNeut' ),
   input.float(defval = 0.5  , minval = -5     , title = 'Neut CMO Z Ratio'                                 , group = 'Range Filter', inline = 'cmoNeut' ),
   input.float(defval = 5    , minval = -10.1  , title = 'Bear CMO Multi'     , tooltip = rngMulti3Tooltip  , group = 'Range Filter', inline = 'cmoBear' ),
   input.float(defval = 0    , minval = -5     , title = 'Bear CMO Z Ratio'                                 , group = 'Range Filter', inline = 'cmoBear' ) 
 )

type AdxSettings
    bool    useAdxFilt
    int     adxLen
    int     adxTh
    int     upperLimit
    int     lowerLimit
    bool    isDynamicLimits
    int     minMaxPeriod
    color   limitsColor
    color   defaultColor

adx_len =   input.int  (defval = 12   , minval=1     , title='Filter Length'                           , group = 'ADX'  , inline = '1'),
adx_th  =   input.int  (defval = 20   , minval=-100  , title='Filter Threshold'                        , group = 'ADX'  , inline = '1'),

AdxSettings adxSettings = 
 AdxSettings.new(
   input.bool (defval = true                , title='Use ADX Filter?'    , tooltip = baseADX           , group = 'ADX'),
   adx_len,
   adx_th,
   input.int  (defval = 60   , minval=50    , title='Upper Limit'        , tooltip = upperLimitDescrp  , group = 'ADX' , inline = '2'),
   input.int  (defval = 15   , minval=1     , title='Lower Limit'        , tooltip = lowerLimitDescrp  , group = 'ADX' , inline = '2'),
   input.bool (defval = true                , title='Use Dyn Limits?'    , tooltip = visualTooltip     , group = 'ADX'),
   input.int  (defval = 84   , minval=2     , title='cmoLen Period'      , tooltip = minMaxPeriodDesc  , group = 'ADX'),
   input.color(defval = COLOR_GRAY          , title='Limits Color'       , tooltip = visualTooltip     , group = 'ADX'),
   input.color(defval = COLOR_BLUE          , title='Default Color'      , tooltip = visualTooltip     , group = 'ADX')
 )

type DisplaySettings
    bool  isEnableTrend
    color bullColor
    color bearColor
    bool  isEnableLimits
    color supplyColor
    color demandColor
    bool  isEnableSlope
    float adxSlopeThres
    float adxSlopeDisp
    color upSlopeColor
    color downSlopeColor
    color neutSlopeColor
    int   slopeLineWidth

rf_slope_width =    input.int  (defval = 1    , minval=1    , maxval=5         , title='Slope Dot Width'        , group = 'Slope Display')

DisplaySettings displaySettings = 
 DisplaySettings.new(
   input.bool (defval = true               , title='Enable Trend Display'      , group = 'Trend Display'                              ),
   input.color(defval = COLOR_TREND_BULL   , title='Bull Trend'                , group = 'Trend Display'        , inline = '21'       ),
   input.color(defval = COLOR_TREND_BEAR   , title='Bear Trend'                , group = 'Trend Display'        , inline = '21'       ),
   input.bool (defval = true               , title='Enable Extremes Display'   , group = 'Supply/Demand Display'                      ),
   input.color(defval = COLOR_SUPPLY       , title='Supply Zone'               , group = 'Supply/Demand Display', inline = '31'       ),
   input.color(defval = COLOR_DEMAND       , title='Demand Zone'               , group = 'Supply/Demand Display', inline = '31'       ),
   input.bool (defval = true               , title='Enable Slope Display'      , group = 'Slope Display'                              ),
   input.float(defval = 15   , minval=0.01 , title='Slope Threshold'           , tooltip = adxSlopeThresDesc , group = 'Slope Display'),
   input.float(defval = 5    , minval=0.01 , title='Slope Dots Displacement'   , tooltip = adxSlopeDispDescrp, group = 'Slope Display'),
   input.color(defval = COLOR_SLOPE_UP     , title='Up Slope'                  , group = 'Slope Display'        , inline = '41'       ),
   input.color(defval = COLOR_SLOPE_DOWN   , title='Down Slope'                , group = 'Slope Display'        , inline = '41'       ),
   input.color(defval = COLOR_SLOPE_NEUTRAL, title='Neutral Slope'             , group = 'Slope Display'        , inline = '41'       ),
   rf_slope_width
 )

tf1 = input.timeframe("15",  "MTF Timeframe #1 (Fastest)", group="Multi-Timeframe")
tf2 = input.timeframe("30", "MTF Timeframe #2", group="Multi-Timeframe")
tf3 = input.timeframe("60", "MTF Timeframe #3", group="Multi-Timeframe")
tf4 = input.timeframe("240",  "MTF Timeframe #4 (Slowest)", group="Multi-Timeframe")

type MTFSignalData
    float   adxValue
    float   adxSlope
    float   adxAccel
    float   rfValue
    float   rfSlope
    float   rfAccel
    float   cmoZ
    float   volumeWeight
    bool    rfBullish
    bool    adxStrong
    int     timeframeWeight

type ConfluenceMatrix
    float   adxAlignment
    float   rfMomentum
    string  cmoRegime
    float   volumeConfirmation
    float   slopeConvergence
    float   accelerationDivergence

type SignalOutput
    bool    buySignal
    bool    sellSignal
    int     confidence
    float   entryPrice
    float   stopLoss
    float   takeProfit
    string  regime
    float   positionSize

signal_group = "Signal Generation"
enable_signals      = input.bool(true, "Enable Signal Generation", group=signal_group)
confidence_threshold = input.int(50, "Min Confidence Level", minval=25, maxval=95, group=signal_group, tooltip="Minimum confidence for signal generation")
regime_sensitivity  = input.float(0.3, "Regime Detection Sensitivity", minval=0.1, maxval=1.0, step=0.1, group=signal_group)
mtf_weight_decay    = input.float(0.85, "MTF Weight Decay Factor", minval=0.5, maxval=0.95, step=0.05, group=signal_group, tooltip="How much to decay older timeframe signals")

risk_group = "Risk Management"
stop_loss_atr_mult  = input.float(2.2, "Stop Loss ATR Multiplier", minval=1.0, maxval=5.0, step=0.1, group=risk_group)
take_profit_ratio   = input.float(1.8, "Take Profit Ratio", minval=1.0, maxval=3.0, step=0.1, group=risk_group)
max_position_size   = input.float(100.0, "Max Position Size %", minval=10.0, maxval=100.0, step=5.0, group=risk_group)
dynamic_sizing      = input.bool(true, "Dynamic Position Sizing", group=risk_group, tooltip="Adjust position size based on signal confidence")

alert_group = "Alert Settings"
enable_alerts       = input.bool(true, "Enable Alerts", group=alert_group)
webhook_format      = input.bool(false, "Webhook JSON Format", group=alert_group, tooltip="Format alerts for trading bot integration")
alert_frequency     = input.string("Once Per Bar", "Alert Frequency", options=["Once Per Bar", "Once Per Bar Close"], group=alert_group)

institutional_group = "Institutional Risk Controls"
max_daily_loss_pct = input.float(10.0, "Max Daily Loss %", minval=1.0, maxval=20.0, step=0.5, group=institutional_group, tooltip="Circuit breaker - stop trading after daily loss")
max_consecutive_losses = input.int(5, "Max Consecutive Losses", minval=1, maxval=10, group=institutional_group, tooltip="Reduce position size after consecutive losses")
volatility_filter_enabled = input.bool(false, "Enable Volatility Filter", group=institutional_group, tooltip="Filter trades during extreme volatility")
volatility_zscore_threshold = input.float(3.0, "Volatility Z-Score Threshold", minval=1.0, maxval=5.0, step=0.1, group=institutional_group)
liquidity_filter_enabled = input.bool(false, "Enable Liquidity Filter", group=institutional_group, tooltip="Filter trades during low liquidity periods")
min_volume_multiplier = input.float(0.2, "Min Volume Multiplier", minval=0.1, maxval=2.0, step=0.1, group=institutional_group, tooltip="Minimum volume vs 20-period average")

cross_asset_group = "Cross-Asset Analysis"
btc_correlation_enabled = input.bool(true, "Enable BTC Correlation", group=cross_asset_group, tooltip="Confirm ETH signals with BTC trend")
btc_correlation_period = input.int(20, "BTC Correlation Period", minval=5, maxval=50, group=cross_asset_group)
min_correlation_threshold = input.float(0.3, "Min Correlation Threshold", minval=0.0, maxval=1.0, step=0.1, group=cross_asset_group)
total_crypto_confirmation = input.bool(true, "Total Crypto Market Confirmation", group=cross_asset_group, tooltip="Confirm with total crypto market cap")

execution_group = "Execution Quality"
max_slippage_pct = input.float(0.1, "Max Slippage %", minval=0.01, maxval=1.0, step=0.01, group=execution_group, tooltip="Maximum acceptable slippage")
use_limit_orders = input.bool(true, "Use Limit Orders", group=execution_group, tooltip="Use limit orders for better execution")
limit_order_offset_pct = input.float(0.05, "Limit Order Offset %", minval=0.01, maxval=0.5, step=0.01, group=execution_group)
partial_fill_timeout = input.int(5, "Partial Fill Timeout (bars)", minval=1, maxval=20, group=execution_group)

calc_adx(length) =>
    ta.dmi(length, length)

calc_adx_limits(adxValue, useDynamic, upperPct, lowerPct, period) =>
    adxHigh = ta.highest(adxValue, period)
    adxLow = ta.lowest(adxValue, period)
    adxRange = adxHigh - adxLow
    upperLimit = useDynamic ? adxLow + upperPct * adxRange / 100 : upperPct
    lowerLimit = useDynamic ? adxLow + lowerPct * adxRange / 100 : lowerPct
    centerLine = (upperLimit + lowerLimit) / 2
    limitColor = adxValue > upperLimit ? color.green : adxValue < lowerLimit ? color.red : color.gray
    [upperLimit, lowerLimit, centerLine, limitColor]

calc_slope(feature, threshold, disp, upColor, downColor, neutColor) =>
    slope = math.atan((feature - feature[1]) / 2.0) * 180.0 / math.pi
    slopeColor = slope > threshold ? downColor : slope < -threshold ? upColor : neutColor
    [feature + disp, slopeColor]

calc_volatility_regime() =>
    atr_current = ta.atr(14)
    atr_mean = ta.sma(atr_current, 50)
    atr_std = ta.stdev(atr_current, 50)
    volatility_zscore = (atr_current - atr_mean) / atr_std
    extreme_volatility = math.abs(volatility_zscore) > volatility_zscore_threshold
    volatility_regime = volatility_zscore > 1.5 ? "high" : volatility_zscore < -1.0 ? "low" : "normal"
    [volatility_zscore, extreme_volatility, volatility_regime]

calc_liquidity_conditions() =>
    volume_mean = ta.sma(volume, 20)
    volume_ratio = volume / volume_mean
    low_liquidity = volume_ratio < min_volume_multiplier
    spread_proxy = (high - low) / close
    spread_mean = ta.sma(spread_proxy, 20)
    wide_spread = spread_proxy > spread_mean * 2.0
    liquidity_score = volume_ratio * (1 - math.min(spread_proxy / spread_mean, 2.0))
    [volume_ratio, low_liquidity, wide_spread, liquidity_score]

calc_cross_asset_confirmation() =>
    btc_proxy = request.security("BINANCE:BTCUSDT", timeframe.period, close, ignore_invalid_symbol=true)
    eth_returns = ta.roc(close, 1)
    btc_returns = ta.roc(btc_proxy, 1)
    correlation = ta.correlation(eth_returns, btc_returns, btc_correlation_period)
    correlation_ok = not btc_correlation_enabled or (not na(correlation) and math.abs(correlation) > min_correlation_threshold)
    market_structure_bullish = close > ta.sma(close, 50) and ta.sma(close, 20) > ta.sma(close, 50)
    market_structure_bearish = close < ta.sma(close, 50) and ta.sma(close, 20) < ta.sma(close, 50)
    [correlation, correlation_ok, market_structure_bullish, market_structure_bearish]

calc_execution_quality() =>
    spread_estimate = (high - low) / close * 100
    slippage_estimate = spread_estimate * 0.5
    execution_feasible = slippage_estimate < max_slippage_pct
    hour_of_day = hour(time)
    high_liquidity_hours = (hour_of_day >= 8 and hour_of_day <= 16) or (hour_of_day >= 20 and hour_of_day <= 24)
    [spread_estimate, slippage_estimate, execution_feasible, high_liquidity_hours]

// === RISK MANAGEMENT FRAMEWORK ===
var float daily_pnl = 0.0
var int consecutive_losses = 0
var float portfolio_heat = 0.0
var bool circuit_breaker_active = false
var bool riskBudgetOk = false

if dayofweek != dayofweek[1]
    daily_pnl := 0.0
    circuit_breaker_active := false

if strategy.position_size == 0 and strategy.position_size[1] != 0
    trade_pnl = strategy.netprofit - strategy.netprofit[1]
    daily_pnl := daily_pnl + trade_pnl
    if trade_pnl < 0
        consecutive_losses := consecutive_losses + 1
    else
        consecutive_losses := 0

daily_loss_exceeded = math.abs(daily_pnl) > max_daily_loss_pct
if daily_loss_exceeded and not circuit_breaker_active
    circuit_breaker_active := true

current_risk = strategy.position_size != 0 ? math.abs(strategy.position_size) * stop_loss_atr_mult / 100 : 0.0
portfolio_heat := current_risk
loss_multiplier = consecutive_losses >= max_consecutive_losses ? 0.5 :
                 consecutive_losses >= 2 ? 0.75 : 1.0
risk_budget_available = portfolio_heat < 15.0

calc_dynamic_position_sizing(baseSize, signalConfidence, volatilityRegime, liquidityScore, lossMultiplier) =>
    confidence_multiplier = signalConfidence / 100
    volatility_multiplier = volatilityRegime == "high" ? 0.5 : volatilityRegime == "low" ? 1.2 : 1.0
    liquidity_multiplier = liquidityScore > 1.0 ? 1.0 : liquidityScore > 0.5 ? 0.7 : 0.3
    risk_multiplier = lossMultiplier
    final_size = baseSize * confidence_multiplier * volatility_multiplier * liquidity_multiplier * risk_multiplier
    math.max(final_size, baseSize * 0.1) // Minimum 10% of base size

mtf_analysis(_src) =>
    cmoRaw = ta.cmo(_src, rf_period)
    cmoZ = (cmoRaw - ta.sma(cmoRaw, rf_cmoZ)) / ta.stdev(cmoRaw, rf_cmoZ)
    mult = cmoZ > rangeFilterSettings.cmoBullZ ? rangeFilterSettings.rangeMulti1 : cmoZ > rangeFilterSettings.cmoNeutZ ? rangeFilterSettings.rangeMulti2 : cmoZ < rangeFilterSettings.cmoBearZ ? rangeFilterSettings.rangeMulti3 : rangeFilterSettings.rangeMult0
    avrng = ta.ema(math.abs(_src - _src[1]), rf_smoothing)
    smrng = ta.ema(avrng, rf_smoothing * 2 - 1) * mult
    
    var float rfVal = na
    rfVal := na(rfVal) ? _src : (_src > rfVal ? math.max(rfVal, _src - smrng) : math.min(rfVal, _src + smrng))
    rfMomentum = rfVal - nz(rfVal[1], rfVal)
    rfAcceleration = rfMomentum - nz(rfVal[1] - rfVal[2], 0)

    [adxValue, _, _] = ta.dmi(adx_len, adx_len)
    adxMomentum = adxValue - nz(adxValue[1], adxValue)
    adxAcceleration = adxMomentum - nz(adxValue[1] - adxValue[2], 0)

    rfBullish = rfMomentum > 0
    adxStrong = adxSettings.useAdxFilt ? adxValue > adxSettings.adxTh : true
    volumeWeight = volume / ta.sma(volume, 20)
    multColor = mult == rangeFilterSettings.rangeMulti1 ? COLOR_CMO_M1 : mult == rangeFilterSettings.rangeMulti2 ? COLOR_CMO_M2 : mult == rangeFilterSettings.rangeMulti3 ? COLOR_CMO_M3 : COLOR_CM0_M0
    [smrng, cmoZ, mult, multColor, rfVal, rfBullish ? 1 : 0, rfBullish ? 0 : 1, adxStrong, rfMomentum, rfAcceleration, adxMomentum, adxAcceleration, volumeWeight, adxValue]

[tf1Smrg, tf1_cmo, tf1_multi, tf1_mColor, rf1Filt, tf1_rfUp, tf1_rfDn, tf1_adxUp, tf1_rfSlope, tf1_rfAccel, tf1_adxSlope, tf1_adxAccel, tf1_volWeight, tf1_adxVal] = request.security(syminfo.tickerid, tf1, mtf_analysis(src))
[tf2Smrg, tf2_cmo, tf2_multi, tf2_mColor, rf2Filt, tf2_rfUp, tf2_rfDn, tf2_adxUp, tf2_rfSlope, tf2_rfAccel, tf2_adxSlope, tf2_adxAccel, tf2_volWeight, tf2_adxVal] = request.security(syminfo.tickerid, tf2, mtf_analysis(src))
[tf3Smrg, tf3_cmo, tf3_multi, tf3_mColor, rf3Filt, tf3_rfUp, tf3_rfDn, tf3_adxUp, tf3_rfSlope, tf3_rfAccel, tf3_adxSlope, tf3_adxAccel, tf3_volWeight, tf3_adxVal] = request.security(syminfo.tickerid, tf3, mtf_analysis(src))
[tf4Smrg, tf4_cmo, tf4_multi, tf4_mColor, rf4Filt, tf4_rfUp, tf4_rfDn, tf4_adxUp, tf4_rfSlope, tf4_rfAccel, tf4_adxSlope, tf4_adxAccel, tf4_volWeight, tf4_adxVal] = request.security(syminfo.tickerid, tf4, mtf_analysis(src))

[adx, plusDI, minusDI]                                          = calc_adx(adx_len)
[adxSlope, adxSlopeColor]                                       = calc_slope(adx, displaySettings.adxSlopeThres, displaySettings.adxSlopeDisp, displaySettings.upSlopeColor, displaySettings.downSlopeColor, displaySettings.neutSlopeColor)
[adxSupplyLine, adxDemandLine, adxCenterLine, adxColor]         = calc_adx_limits(adx, adxSettings.isDynamicLimits, adxSettings.upperLimit, adxSettings.lowerLimit, adxSettings.minMaxPeriod)

calc_confluence_score() =>
    adxAlignment = (tf1_adxVal * 1.0 + tf2_adxVal * 0.85 + tf3_adxVal * 0.7 + tf4_adxVal * 0.55) / 3.1
    rfMomentum = (tf1_rfSlope * 1.0 + tf2_rfSlope * 0.85 + tf3_rfSlope * 0.7 + tf4_rfSlope * 0.55) / 3.1
    cmoRegime = tf1_cmo > 1.0 ? "trending" : tf1_cmo > -1.0 ? "transitional" : "ranging"
    volConfirmation = (tf1_volWeight + tf2_volWeight + tf3_volWeight + tf4_volWeight) * 0.25
    slopeConvergence = math.abs(tf1_rfSlope - tf2_rfSlope) + math.abs(tf2_rfSlope - tf3_rfSlope) + math.abs(tf3_rfSlope - tf4_rfSlope)
    accelDivergence = math.abs(tf1_rfAccel - tf2_rfAccel) + math.abs(tf2_rfAccel - tf3_rfAccel) + math.abs(tf3_rfAccel - tf4_rfAccel)
    [adxAlignment, rfMomentum, cmoRegime, volConfirmation, slopeConvergence, accelDivergence]

generate_enhanced_signals() =>
    [adxAlign, rfMom, regime, volConf, slopeConv, accelDiv] = calc_confluence_score()
    baseThreshold = regime == "trending" ? 0.6 : regime == "transitional" ? 0.8 : 1.0
    rfAgreement = (tf1_rfUp > 0 ? 1 : 0) + (tf2_rfUp > 0 ? 1 : 0) + (tf3_rfUp > 0 ? 1 : 0) + (tf4_rfUp > 0 ? 1 : 0)
    adxAgreement = (tf1_adxUp ? 1 : 0) + (tf2_adxUp ? 1 : 0) + (tf3_adxUp ? 1 : 0) + (tf4_adxUp ? 1 : 0)

    [volZscore, extremeVol, volRegime] = calc_volatility_regime()
    [volRatio, lowLiq, wideSpread, liqScore] = calc_liquidity_conditions()
    [correlation, corrOk, mktBullish, mktBearish] = calc_cross_asset_confirmation()
    [spreadEst, slippageEst, execFeasible, highLiqHours] = calc_execution_quality()

    dailyLossExceeded = daily_loss_exceeded
    riskBudgetOk = risk_budget_available
    circuitBreakerActive = circuit_breaker_active

    technical_signal_long = rfAgreement >= 3 and adxAgreement >= 2 and rfMom > baseThreshold and (tf1_rfAccel + tf1_adxAccel) > 0
    technical_signal_short = rfAgreement <= 1 and adxAgreement <= 2 and rfMom < -baseThreshold and (tf1_rfAccel + tf1_adxAccel) < 0

    market_structure_ok = not extremeVol or not volatility_filter_enabled
    liquidity_ok = not lowLiq or not liquidity_filter_enabled
    execution_ok = execFeasible and (highLiqHours or not liquidity_filter_enabled)
    cross_asset_ok = corrOk and (technical_signal_long ? mktBullish : technical_signal_short ? mktBearish : true)

    risk_management_ok = not circuitBreakerActive and riskBudgetOk and not dailyLossExceeded

    buySignal = technical_signal_long and market_structure_ok and liquidity_ok and execution_ok and cross_asset_ok and risk_management_ok
    sellSignal = technical_signal_short and market_structure_ok and liquidity_ok and execution_ok and cross_asset_ok and risk_management_ok

    base_confidence = (rfAgreement + adxAgreement) * 12.5
    volatility_penalty = extremeVol ? 0.7 : 1.0
    liquidity_penalty = lowLiq ? 0.8 : 1.0
    execution_penalty = not execFeasible ? 0.6 : 1.0
    correlation_bonus = math.abs(correlation) > 0.7 ? 1.2 : 1.0

    confidence = math.round(base_confidence * volConf * volatility_penalty * liquidity_penalty * execution_penalty * correlation_bonus * (1 - slopeConv * 0.1) * (1 - accelDiv * 0.1))
    confidence := math.max(0, math.min(100, confidence))

    enhanced_regime = extremeVol ? "volatile_" + regime : regime

    [buySignal, sellSignal, confidence, enhanced_regime, base_confidence, volRegime, liqScore, correlation]

[buySignal, sellSignal, signalConfidence, marketRegime, baseStrength, volRegime, liquidityScore, btcCorrelation] = generate_enhanced_signals()

[volZscore, extremeVol, volRegimeData] = calc_volatility_regime()
[volRatio, lowLiq, wideSpread, liqScore] = calc_liquidity_conditions()

base_position_size = dynamic_sizing ? (signalConfidence / 100) * max_position_size : max_position_size
enhanced_position_size = calc_dynamic_position_sizing(base_position_size, signalConfidence, volRegimeData, liquidityScore, loss_multiplier)

atr = ta.atr(14)
regime_stop_multiplier = volRegimeData == "high" ? stop_loss_atr_mult * 1.5 : volRegimeData == "low" ? stop_loss_atr_mult * 0.8 : stop_loss_atr_mult
regime_tp_multiplier = marketRegime == "trending" ? take_profit_ratio * 1.5 : marketRegime == "ranging" ? take_profit_ratio * 0.8 : take_profit_ratio

stopLoss = buySignal ? close - (atr * regime_stop_multiplier) : sellSignal ? close + (atr * regime_stop_multiplier) : na
takeProfit = buySignal ? close + (atr * regime_stop_multiplier * regime_tp_multiplier) : sellSignal ? close - (atr * regime_stop_multiplier * regime_tp_multiplier) : na

limit_buy_price = use_limit_orders and buySignal ? close * (1 - limit_order_offset_pct / 100) : na
limit_sell_price = use_limit_orders and sellSignal ? close * (1 + limit_order_offset_pct / 100) : na

var bool circuitBreakerActive = false
var float dailyLossExceeded = 0
if enable_signals and signalConfidence >= confidence_threshold and not circuitBreakerActive
    if buySignal and not strategy.position_size and riskBudgetOk
        entry_comment = "Enhanced Long | Conf:" + str.tostring(signalConfidence) + "% | Regime:" + marketRegime + " | Vol:" + volRegimeData + " | Corr:" + str.tostring(math.round(btcCorrelation, 2))

        if use_limit_orders and not na(limit_buy_price)
            strategy.entry("Long", strategy.long, qty=enhanced_position_size/100, limit=limit_buy_price, comment=entry_comment)
        else
            strategy.entry("Long", strategy.long, qty=enhanced_position_size/100, comment=entry_comment)

        strategy.exit("Long Exit", "Long", stop=stopLoss, limit=takeProfit, comment="Long Exit | SL:" + str.tostring(math.round(regime_stop_multiplier, 1)) + " | TP:" + str.tostring(math.round(regime_tp_multiplier, 1)))

    if sellSignal and not strategy.position_size and riskBudgetOk
        entry_comment = "Enhanced Short | Conf:" + str.tostring(signalConfidence) + "% | Regime:" + marketRegime + " | Vol:" + volRegimeData + " | Corr:" + str.tostring(math.round(btcCorrelation, 2))

        if use_limit_orders and not na(limit_sell_price)
            strategy.entry("Short", strategy.short, qty=enhanced_position_size/100, limit=limit_sell_price, comment=entry_comment)
        else
            strategy.entry("Short", strategy.short, qty=enhanced_position_size/100, comment=entry_comment)

        strategy.exit("Short Exit", "Short", stop=stopLoss, limit=takeProfit, comment="Short Exit | SL:" + str.tostring(math.round(regime_stop_multiplier, 1)) + " | TP:" + str.tostring(math.round(regime_tp_multiplier, 1)))

if strategy.position_size != 0 and (circuitBreakerActive or dailyLossExceeded)
    strategy.close_all(comment="Emergency Exit - Risk Controls Triggered")

if enable_alerts and signalConfidence >= confidence_threshold and not circuitBreakerActive
    if buySignal and not buySignal[1]
        alert_msg = webhook_format ?
         '{"action":"buy","symbol":"ETHUSDT","confidence":' + str.tostring(signalConfidence) + ',"regime":"' + marketRegime + '","price":' + str.tostring(close) + ',"volatility":"' + volRegimeData + '","liquidity":' + str.tostring(math.round(liquidityScore, 2)) + ',"btc_correlation":' + str.tostring(math.round(btcCorrelation, 2)) + ',"position_size":' + str.tostring(math.round(enhanced_position_size, 1)) + ',"stop_loss":' + str.tostring(math.round(stopLoss, 2)) + ',"take_profit":' + str.tostring(math.round(takeProfit, 2)) + '}' :
         "ENHANCED BUY: ETH/USDT | Conf:" + str.tostring(signalConfidence) + "% | Regime:" + marketRegime + " | Vol:" + volRegimeData + " | Liq:" + str.tostring(math.round(liquidityScore, 2)) + " | BTC Corr:" + str.tostring(math.round(btcCorrelation, 2)) + " | Size:" + str.tostring(math.round(enhanced_position_size, 1)) + "% | Price:" + str.tostring(close)
        alert(alert_msg, alert_frequency == "Once Per Bar" ? alert.freq_once_per_bar : alert.freq_once_per_bar_close)

    if sellSignal and not sellSignal[1]
        alert_msg = webhook_format ?
         '{"action":"sell","symbol":"ETHUSDT","confidence":' + str.tostring(signalConfidence) + ',"regime":"' + marketRegime + ',"price":' + str.tostring(close) + ',"volatility":"' + volRegimeData + '","liquidity":' + str.tostring(math.round(liquidityScore, 2)) + ',"btc_correlation":' + str.tostring(math.round(btcCorrelation, 2)) + ',"position_size":' + str.tostring(math.round(enhanced_position_size, 1)) + ',"stop_loss":' + str.tostring(math.round(stopLoss, 2)) + ',"take_profit":' + str.tostring(math.round(takeProfit, 2)) + '}' :
         "ENHANCED SELL: ETH/USDT | Conf:" + str.tostring(signalConfidence) + "% | Regime:" + marketRegime + " | Vol:" + volRegimeData + " | Liq:" + str.tostring(math.round(liquidityScore, 2)) + " | BTC Corr:" + str.tostring(math.round(btcCorrelation, 2)) + " | Size:" + str.tostring(math.round(enhanced_position_size, 1)) + "% | Price:" + str.tostring(close)
        alert(alert_msg, alert_frequency == "Once Per Bar" ? alert.freq_once_per_bar : alert.freq_once_per_bar_close)

if circuitBreakerActive and not circuitBreakerActive[1]
    alert("CIRCUIT BREAKER ACTIVATED: Daily loss limit exceeded. Trading halted.", alert.freq_once_per_bar)

if dailyLossExceeded and not dailyLossExceeded[1]
    alert("RISK WARNING: Daily loss threshold reached. Review risk parameters.", alert.freq_once_per_bar)

plot(-10, title="RF1 ", style=plot.style_circles, linewidth=1, color=tf1_rfUp  ? color.green : color.red)
plot(-13, title="ADX1", style=plot.style_circles, linewidth=1, color=tf1_adxUp ? color.green : color.red)
plot(-20, title="RF2",  style=plot.style_circles, linewidth=1, color=tf2_rfUp  ? color.green : color.red)
plot(-23, title="ADX2", style=plot.style_circles, linewidth=1, color=tf2_adxUp ? color.green : color.red)
plot(-30, title="RF3",  style=plot.style_circles, linewidth=1, color=tf3_rfUp  ? color.green : color.red)
plot(-33, title="ADX3", style=plot.style_circles, linewidth=1, color=tf3_adxUp ? color.green : color.red)
plot(-40, title="RF4",  style=plot.style_circles, linewidth=1, color=tf4_rfUp  ? color.green : color.red)
plot(-43, title="ADX4", style=plot.style_circles, linewidth=1, color=tf4_adxUp ? color.green : color.red)

plot(signalConfidence, title="Enhanced Signal Confidence", color=color.new(color.white, 0), linewidth=2)
plot(baseStrength, title="Base Signal Strength", color=color.new(color.yellow, 0), linewidth=1)
plot(enhanced_position_size, title="Dynamic Position Size", color=color.new(color.orange, 0), linewidth=1)

plot(circuitBreakerActive ? 95 : na, title="Circuit Breaker", color=color.new(color.red, 0), style=plot.style_cross, linewidth=6)
plotshape(dailyLossExceeded ? 90 : na, title="Daily Loss Warning", color=color.new(color.orange, 0), style=shape.xcross, force_overlay = true)
plotshape(extremeVol ? 85 : na, title="Extreme Volatility", color=color.new(color.purple, 0), style=shape.diamond, force_overlay = true)
plotshape(lowLiq ? 80 : na, title="Low Liquidity", color=color.new(color.blue, 0), style=shape.square, force_overlay = true)

enhanced_buy_color = buySignal and signalConfidence >= confidence_threshold and not circuitBreakerActive ? color.new(color.lime, 0) : na
enhanced_sell_color = sellSignal and signalConfidence >= confidence_threshold and not circuitBreakerActive ? color.new(color.red, 0) : na

plotshape(buySignal and signalConfidence >= confidence_threshold ? 75 : na, title="Enhanced Buy Signal", color=enhanced_buy_color, style=shape.triangleup, force_overlay = true)
plotshape(sellSignal and signalConfidence >= confidence_threshold ? 5 : na, title="Enhanced Sell Signal", color=enhanced_sell_color, style=shape.triangledown, force_overlay = true)

// Market regime and correlation monitoring
plot(volZscore * 10, title="Volatility Z-Score (x10)", color=color.new(color.gray, 50), linewidth=1)
plot(btcCorrelation * 50, title="BTC Correlation (x50)", color=color.new(color.silver, 50), linewidth=1)
plot(liquidityScore * 20, title="Liquidity Score (x20)", color=color.new(color.aqua, 50), linewidth=1)
plot(tf1_cmo, title="CMO Z-Score", color=tf1_mColor, linewidth=2, style=plot.style_columns)
plot(adxSupplyLine, title="ADX Upper Limit", color=adxSettings.limitsColor)
plot(adxDemandLine, title="ADX Lower Limit", color=adxSettings.limitsColor)
plot(adxCenterLine, title="ADX Center Line", color=adxSettings.limitsColor)
plot(adx, title="ADX", color=adxColor, linewidth=2)
plot(displaySettings.isEnableSlope ? adxSlope : na, title="ADX Slope", color=adxSlopeColor, style=plot.style_circles, linewidth=rf_slope_width)
filtPlot = plot(rf1Filt, color=tf1_rfUp > 0 ? color.rgb(0, 230, 118) : tf1_rfDn > 0 ? color.rgb(255, 82, 82) : color.rgb(255, 152, 0), linewidth=1, title='Range Filter TF1', force_overlay=true)
hBand = rf1Filt + tf1Smrg
lBand = rf1Filt - tf1Smrg
plot(hBand, color=color.new(color.aqua, 100), title='High Target', force_overlay=true)
plot(lBand, color=color.new(color.fuchsia, 100), title='Low Target', force_overlay=true)
fill(plot(hBand, force_overlay=true, display=display.data_window), plot(rf1Filt, force_overlay=true, display=display.data_window), color=color.new(color.aqua, 90), title='High Target Range')
fill(plot(lBand, force_overlay=true, display=display.data_window), plot(rf1Filt, force_overlay=true, display=display.data_window), color=color.new(color.fuchsia, 90), title='Low Target Range')
plot(rf2Filt, color=tf2_rfUp > 0 ? color.rgb(0, 230, 118, 20) : tf2_rfDn > 0 ? color.rgb(255, 82, 82, 20) : color.rgb(255, 152, 0, 20), linewidth=2, title='Range Filter TF2', force_overlay=true)
plot(rf3Filt, color=tf3_rfUp > 0 ? color.rgb(0, 230, 118, 40) : tf3_rfDn > 0 ? color.rgb(255, 82, 82, 40) : color.rgb(255, 152, 0, 40), linewidth=3, title='Range Filter TF3', force_overlay=true)
plot(rf4Filt, color=tf4_rfUp > 0 ? color.rgb(0, 230, 118, 60) : tf4_rfDn > 0 ? color.rgb(255, 82, 82, 60) : color.rgb(255, 152, 0, 60), linewidth=4, title='Range Filter TF4', force_overlay=true)